import { NextRequest, NextResponse } from 'next/server';
import { withAdminAuth } from '@/lib/admin-middleware';
import { supabase } from '@/lib/supabase';
import { z } from 'zod';

/**
 * PATCH /api/admin/reviews/[id]/status
 * Admin endpoint to update review status (approve/reject)
 * Following HAQ-rules.md ADMIN-03 specification
 * 
 * SECURITY REQUIREMENTS:
 * - Requires valid JWT with admin role (enforced by withAdminAuth)
 * - Validates review exists and is in pending status
 * - Updates status to 'approved' or 'rejected'
 * - Logs admin action for audit trail
 * 
 * Path Parameters:
 * - id: Review UUID
 * 
 * Request Body:
 * - status: 'approved' | 'rejected'
 * - reason?: Optional reason for rejection (admin notes)
 */

// Validation schema for status update
const statusUpdateSchema = z.object({
  status: z.enum(['approved', 'rejected'], {
    errorMap: () => ({ message: 'Status must be either "approved" or "rejected"' })
  }),
  reason: z.string().optional().transform(val => val?.trim() || undefined)
});

async function handleUpdateReviewStatus(
  request: NextRequest,
  context: { params: { id: string } },
  user: any
): Promise<NextResponse> {
  try {
    const reviewId = context.params.id;
    
    // Validate review ID format
    if (!reviewId || typeof reviewId !== 'string') {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Invalid review ID format' 
        },
        { status: 400 }
      );
    }
    
    // Parse and validate request body
    let requestBody;
    try {
      requestBody = await request.json();
    } catch (error) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Invalid JSON in request body' 
        },
        { status: 400 }
      );
    }
    
    // Validate request data
    const validationResult = statusUpdateSchema.safeParse(requestBody);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Validation failed',
          errors: validationResult.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      );
    }
    
    const { status, reason } = validationResult.data;
    
    // Check if review exists and is pending approval (content schema - RULE-102)
    const { data: existingReview, error: fetchError } = await supabase
      .from('haq_content_db.reviews')
      .select('review_id, company_id, is_approved')
      .eq('review_id', reviewId)
      .single();
    
    if (fetchError || !existingReview) {
      return NextResponse.json(
        {
          success: false,
          message: 'Review not found'
        },
        { status: 404 }
      );
    }

    // Get company information separately (schema isolation - RULE-102)
    const { data: company, error: companyError } = await supabase
      .from('haq_content_db.companies')
      .select('company_id, name')
      .eq('company_id', existingReview.company_id)
      .single();

    if (companyError || !company) {
      return NextResponse.json(
        {
          success: false,
          message: 'Company not found for this review'
        },
        { status: 404 }
      );
    }

    // Check if review is already approved
    if (existingReview.is_approved === true) {
      return NextResponse.json(
        {
          success: false,
          message: 'Review is already approved. Only pending reviews can be moderated.'
        },
        { status: 400 }
      );
    }
    
    // Update review approval status (content schema - RULE-102)
    const updateData: any = {
      is_approved: status === 'approved',
      updated_at: new Date().toISOString()
    };

    // For rejected reviews, we could add a rejection reason field if needed
    // But for anonymity, we keep minimal data

    const { data: updatedReview, error: updateError } = await supabase
      .from('haq_content_db.reviews')
      .update(updateData)
      .eq('review_id', reviewId)
      .select('review_id, company_id, is_approved, updated_at')
      .single();
    
    if (updateError) {
      console.error('Error updating review status:', updateError);
      return NextResponse.json(
        {
          success: false,
          message: 'Failed to update review status'
        },
        { status: 500 }
      );
    }

    // ANONYMITY PROTECTION: No admin identification in logs per RULE-601
    console.info(`Review ${reviewId} ${status} for company ${company.name}`);

    // Return success response
    return NextResponse.json({
      success: true,
      message: `Review ${status} successfully`,
      data: {
        review_id: updatedReview.review_id,
        company: {
          company_id: company.company_id,
          name: company.name
        },
        is_approved: updatedReview.is_approved,
        updated_at: updatedReview.updated_at
      }
    }, {
      headers: {
        // No caching for admin actions
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY'
      }
    });
    
  } catch (error) {
    console.error('Admin review status update error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

// Export the PATCH handler wrapped with admin authentication
export const PATCH = withAdminAuth(handleUpdateReviewStatus);

// Only allow PATCH requests
export async function GET() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'PATCH' } }
  );
}

export async function POST() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'PATCH' } }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'PATCH' } }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'PATCH' } }
  );
}
