(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{283:(e,t,s)=>{"use strict";s.d(t,{As:()=>l,AuthProvider:()=>o});var r=s(5155),a=s(2115);let n={user:null,isLoading:!0,isAuthenticated:!1,error:null};function c(e,t){switch(t.type){case"AUTH_START":return{...e,isLoading:!0,error:null};case"AUTH_SUCCESS":return{...e,user:t.payload,isAuthenticated:!0,isLoading:!1,error:null};case"AUTH_FAILURE":return{...e,user:null,isAuthenticated:!1,isLoading:!1,error:t.payload};case"AUTH_LOGOUT":return{...e,user:null,isAuthenticated:!1,isLoading:!1,error:null};case"CLEAR_ERROR":return{...e,error:null};case"SET_LOADING":return{...e,isLoading:t.payload};default:return e}}let i=(0,a.createContext)(null);function o(e){let{children:t}=e,[s,o]=(0,a.useReducer)(c,n);(0,a.useEffect)(()=>{l()},[]);let l=(0,a.useCallback)(async()=>{try{o({type:"SET_LOADING",payload:!0});let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});if(e.ok){let t=await e.json();t.success&&t.user?o({type:"AUTH_SUCCESS",payload:t.user}):o({type:"AUTH_LOGOUT"})}else o({type:"AUTH_LOGOUT"})}catch(e){o({type:"AUTH_LOGOUT"})}finally{o({type:"SET_LOADING",payload:!1})}},[]),d=(0,a.useCallback)(async(e,t)=>{try{o({type:"AUTH_START"});let s=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),r=await s.json();if(r.success&&r.user)return o({type:"AUTH_SUCCESS",payload:r.user}),{success:!0,message:r.message};return o({type:"AUTH_FAILURE",payload:r.message}),{success:!1,message:r.message}}catch(t){let e="Network error. Please try again.";return o({type:"AUTH_FAILURE",payload:e}),{success:!1,message:e}}},[]),m=(0,a.useCallback)(async(e,t,s)=>{try{o({type:"AUTH_START"});let r=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({username:e,email:t,password:s})}),a=await r.json();if(a.success&&a.user)return o({type:"AUTH_SUCCESS",payload:a.user}),{success:!0,message:a.message};return o({type:"AUTH_FAILURE",payload:a.message}),{success:!1,message:a.message}}catch(t){let e="Network error. Please try again.";return o({type:"AUTH_FAILURE",payload:e}),{success:!1,message:e}}},[]),p=(0,a.useCallback)(async()=>{try{o({type:"SET_LOADING",payload:!0}),await fetch("/api/auth/logout",{method:"POST",credentials:"include"}),o({type:"AUTH_LOGOUT"})}catch(e){o({type:"AUTH_LOGOUT"})}finally{o({type:"SET_LOADING",payload:!1})}},[]),u=(0,a.useCallback)(()=>{o({type:"CLEAR_ERROR"})},[]),x=(0,a.useCallback)(async()=>{await l()},[l]),h=(0,a.useMemo)(()=>({...s,login:d,register:m,logout:p,clearError:u,refreshUser:x}),[s,d,m,p,u,x]);return(0,r.jsx)(i.Provider,{value:h,children:t})}function l(){let e=(0,a.useContext)(i);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},319:(e,t,s)=>{"use strict";s.d(t,{SWRProvider:()=>u});var r=s(5155),a=s(6072),n=s(4982),c=s(9535);let i="https://wqbuilazpyxpwyuwuqpi.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.GeRI54Rskwdbfm9_lRxy1-7YQ8vA74JWblNF2GRpzrI",l=null,d=null,m=()=>(l||(l=(0,c.createBrowserClient)(i,o)),l);d=(0,n.UU)(i,o);let p={fetcher:async e=>{if(m(),e.startsWith("/api/companies"))return[{id:1,company_id:1,name:"TechCorp Solutions",slug:"techcorp-solutions",logo_url:"/placeholder-company.svg",description:"Leading technology solutions provider",location:"Karachi, Pakistan",industry:"Technology",employee_count:"500-1000",haq_score:85,total_reviews:24,redFlags:["High turnover rate","Unpaid overtime"],greenFlags:["Good benefits","Career growth opportunities"]},{id:2,company_id:2,name:"InnovatePak",slug:"innovatepak",logo_url:"/placeholder-company.svg",description:"Innovation-driven software company",location:"Lahore, Pakistan",industry:"Software",employee_count:"100-500",haq_score:78,total_reviews:18,redFlags:["Limited remote work"],greenFlags:["Modern office","Learning opportunities","Flexible hours"]},{id:3,company_id:3,name:"DataFlow Systems",slug:"dataflow-systems",logo_url:"/placeholder-company.svg",description:"Data analytics and business intelligence",location:"Islamabad, Pakistan",industry:"Analytics",employee_count:"50-100",haq_score:72,total_reviews:12,redFlags:["Micromanagement"],greenFlags:["Good work-life balance","Competitive salary"]}];if(e.startsWith("/api/company/")){let t=e.split("/").pop(),s=[{id:1,company_id:1,name:"TechCorp Solutions",slug:"techcorp-solutions",logo_url:"/placeholder-company.svg",description:"Leading technology solutions provider",location:"Karachi, Pakistan",industry:"Technology",employee_count:"500-1000",haq_score:85,total_reviews:24,redFlags:["High turnover rate","Unpaid overtime"],greenFlags:["Good benefits","Career growth opportunities"]}];return s.find(e=>e.slug===t)||s[0]}let t=await fetch(e);if(!t.ok)throw Error("Failed to fetch");return t.json()},revalidateOnFocus:!1,revalidateOnReconnect:!0,refreshInterval:0,errorRetryCount:3,errorRetryInterval:5e3,onError:e=>{console.error("SWR Error:",e)},onSuccess:(e,t)=>{}};function u(e){let{children:t}=e;return(0,r.jsx)(a.BE,{value:p,children:t})}},347:()=>{},5430:(e,t,s)=>{"use strict";s.d(t,{Header:()=>b});var r=s(5155),a=s(2115),n=s(6874),c=s.n(n),i=s(5695),o=s(5525),l=s(5868),d=s(1366),m=s(7580),p=s(1007),u=s(381),x=s(4835),h=s(306),y=s(4416),f=s(4783),g=s(283);let b=()=>{let[e,t]=(0,a.useState)(!1),[s,n]=(0,a.useState)(!1),b=(0,i.usePathname)(),{user:v,isAuthenticated:j,isLoading:N,logout:w}=(0,g.As)(),A=[{name:"Companies",href:"/companies",icon:o.A},{name:"Salaries",href:"/salaries",icon:l.A},{name:"Community",href:"/community",icon:d.A}],_=async()=>{try{await w(),n(!1)}catch(e){console.error("Logout failed:",e)}};return(0,r.jsxs)("header",{className:"bg-background-primary border-b border-border-primary sticky top-0 z-50",children:[(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)(c(),{href:"/",className:"flex items-center space-x-2 group",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200",children:(0,r.jsx)(o.A,{className:"w-5 h-5 text-text-on-accent"})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-xl font-bold text-accent-primary",children:"Haq"}),(0,r.jsx)("span",{className:"text-xs text-text-secondary -mt-1",children:"حق"})]})]}),(0,r.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:A.map(e=>{let t=e.icon,s=b===e.href;return(0,r.jsxs)(c(),{href:e.href,className:"flex items-center space-x-1 px-3 py-2 rounded-lg text-navigation-link font-medium transition-all duration-200 uppercase tracking-wide ".concat(s?"text-accent-primary bg-surface-secondary":"text-text-secondary hover:text-accent-primary hover:bg-surface-primary"),children:[(0,r.jsx)(t,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:e.name})]},e.name)})}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[j&&(0,r.jsxs)(c(),{href:"/review/submit",className:"bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 flex items-center space-x-2 hover:shadow-glow transform hover:-translate-y-0.5",children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Write Review"})]}),N?(0,r.jsx)("div",{className:"w-8 h-8 animate-spin rounded-full border-2 border-accent-primary border-t-transparent"}):j&&v?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>n(!s),className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-text-secondary hover:text-accent-primary hover:bg-surface-primary transition-all duration-200",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-full flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"w-4 h-4 text-text-on-accent"})}),(0,r.jsx)("span",{className:"text-sm font-medium",children:v.username}),(0,r.jsx)("svg",{className:"w-4 h-4 transition-transform duration-200 ".concat(s?"rotate-180":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),s&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-surface-primary border border-border-primary rounded-lg shadow-lg py-1 z-50",children:[(0,r.jsxs)("div",{className:"px-4 py-2 border-b border-border-primary",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-text-primary",children:v.username}),(0,r.jsx)("p",{className:"text-xs text-text-secondary",children:v.email}),(0,r.jsx)("span",{className:"inline-block px-2 py-1 text-xs rounded-full mt-1 ".concat("admin"===v.role?"bg-accent-primary text-text-on-accent":"bg-surface-secondary text-text-secondary"),children:v.role})]}),(0,r.jsxs)(c(),{href:"/profile",onClick:()=>n(!1),className:"flex items-center space-x-2 px-4 py-2 text-sm text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200",children:[(0,r.jsx)(u.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Profile Settings"})]}),(0,r.jsxs)("button",{onClick:_,className:"w-full flex items-center space-x-2 px-4 py-2 text-sm text-text-secondary hover:text-red-600 hover:bg-surface-secondary transition-colors duration-200",children:[(0,r.jsx)(x.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Sign Out"})]})]})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)(c(),{href:"/auth/login",className:"flex items-center space-x-1 px-4 py-2 text-sm font-medium text-text-secondary hover:text-accent-primary transition-colors duration-200",children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Sign In"})]}),(0,r.jsxs)(c(),{href:"/auth/signup",className:"bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 flex items-center space-x-1 hover:shadow-glow transform hover:-translate-y-0.5",children:[(0,r.jsx)(p.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Sign Up"})]})]})]}),(0,r.jsx)("button",{onClick:()=>t(!e),className:"md:hidden p-2 rounded-lg text-text-secondary hover:text-accent-primary hover:bg-surface-primary transition-colors duration-200",children:e?(0,r.jsx)(y.A,{className:"w-6 h-6"}):(0,r.jsx)(f.A,{className:"w-6 h-6"})})]})}),e&&(0,r.jsx)("div",{className:"md:hidden bg-surface-primary border-t border-border-primary",children:(0,r.jsxs)("div",{className:"px-4 py-3 space-y-3",children:[A.map(e=>{let s=e.icon,a=b===e.href;return(0,r.jsxs)(c(),{href:e.href,onClick:()=>t(!1),className:"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ".concat(a?"text-accent-primary bg-surface-secondary":"text-text-secondary hover:text-accent-primary hover:bg-surface-secondary"),children:[(0,r.jsx)(s,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:e.name})]},e.name)}),j?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(c(),{href:"/review/submit",onClick:()=>t(!1),className:"flex items-center space-x-3 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200",children:[(0,r.jsx)(m.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Write Review"})]}),(0,r.jsxs)("div",{className:"border-t border-border-primary pt-3 mt-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 px-3 py-2 mb-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-full flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"w-4 h-4 text-text-on-accent"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-text-primary",children:null==v?void 0:v.username}),(0,r.jsx)("p",{className:"text-xs text-text-secondary",children:null==v?void 0:v.email})]})]}),(0,r.jsxs)(c(),{href:"/profile",onClick:()=>t(!1),className:"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200",children:[(0,r.jsx)(u.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Profile Settings"})]}),(0,r.jsxs)("button",{onClick:()=>{_(),t(!1)},className:"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-red-600 hover:bg-surface-secondary transition-colors duration-200",children:[(0,r.jsx)(x.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Sign Out"})]})]})]}):(0,r.jsxs)("div",{className:"border-t border-border-primary pt-3 mt-3 space-y-2",children:[(0,r.jsxs)(c(),{href:"/auth/login",onClick:()=>t(!1),className:"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200",children:[(0,r.jsx)(h.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Sign In"})]}),(0,r.jsxs)(c(),{href:"/auth/signup",onClick:()=>t(!1),className:"flex items-center space-x-3 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200",children:[(0,r.jsx)(p.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Sign Up"})]})]})]})})]})}},7999:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.t.bind(s,8524,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,5430)),Promise.resolve().then(s.bind(s,283)),Promise.resolve().then(s.bind(s,319))}},e=>{var t=t=>e(e.s=t);e.O(0,[80,244,986,131,441,684,358],()=>t(7999)),_N_E=e.O()}]);