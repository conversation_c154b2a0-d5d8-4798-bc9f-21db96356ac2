import { NextRequest, NextResponse } from 'next/server';
import { ServerAuthHelpers } from '@/lib/auth-server';
import { supabase } from '@/lib/supabase';
import { z } from 'zod';
import DOMPurify from 'dompurify';
import { <PERSON><PERSON><PERSON> } from 'jsdom';
import crypto from 'crypto';

// Create DOMPurify instance for server-side sanitization
const window = new JSDOM('').window;
const purify = DOMPurify(window as any);

/**
 * Generate anonymous user hash for review attribution
 * ANONYMITY PROTECTION: Creates a unique hash per user per company
 * This prevents cross-company correlation while allowing duplicate review detection
 * @param userId - User ID from JWT
 * @param companyId - Company ID for the review
 * @returns Anonymous hash string
 */
function generateAnonymousUserHash(userId: string, companyId: string): string {
  const secret = process.env.ANONYMOUS_HASH_SECRET || 'default-secret-change-in-production';
  const data = `${userId}:${companyId}:${secret}`;
  return crypto.createHash('sha256').update(data).digest('hex').substring(0, 16);
}

// Validation schema for review submission
const reviewSubmissionSchema = z.object({
  company_id: z.string().uuid('Invalid company ID format'),
  overall_rating: z.number().int().min(1, 'Rating must be at least 1').max(5, 'Rating must be at most 5'),
  pros: z.string().optional(),
  cons: z.string().optional(),
  advice_management: z.string().optional(),
});

/**
 * Sanitize text input to prevent XSS attacks
 * Following RULE-603: INPUT_SANITIZATION
 */
function sanitizeText(text: string | undefined): string | null {
  if (!text || typeof text !== 'string') {
    return null;
  }
  
  // Remove any HTML tags and sanitize content
  const sanitized = purify.sanitize(text, {
    ALLOWED_TAGS: [], // No HTML tags allowed
    ALLOWED_ATTR: [], // No attributes allowed
    KEEP_CONTENT: true, // Keep text content
  });
  
  return sanitized.trim() || null;
}

/**
 * Check for potential PII in text content
 * Simple keyword-based detection for MVP
 */
function detectPotentialPII(text: string): string[] {
  const piiKeywords = [
    'manager', 'ceo', 'director', 'supervisor', 'boss', 'lead',
    'john', 'jane', 'smith', 'johnson', 'williams', 'brown', 'jones',
    'email', 'phone', 'address', 'linkedin', 'facebook', 'twitter',
    'my name', 'i am', 'called me', 'told me personally'
  ];
  
  const lowerText = text.toLowerCase();
  const detectedKeywords: string[] = [];
  
  for (const keyword of piiKeywords) {
    if (lowerText.includes(keyword)) {
      detectedKeywords.push(keyword);
    }
  }
  
  return detectedKeywords;
}

/**
 * POST /api/reviews
 * Submit a new review for a company
 * Following HAQ-rules.md AUTH-03 specification
 * Requires authentication, implements anonymity protection
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const isAuthenticated = await ServerAuthHelpers.isAuthenticated();
    if (!isAuthenticated) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Authentication required to submit reviews' 
        },
        { status: 401 }
      );
    }

    // Get current user
    const currentUser = await ServerAuthHelpers.getCurrentUser();
    if (!currentUser) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Failed to retrieve user information' 
        },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = reviewSubmissionSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Validation failed',
          errors: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const reviewData = validationResult.data;

    // Verify company exists in content schema (RULE-102)
    const { data: company, error: companyError } = await supabase
      .from('haq_content_db.companies')
      .select('company_id, name')
      .eq('company_id', reviewData.company_id)
      .single();

    if (companyError || !company) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Company not found' 
        },
        { status: 404 }
      );
    }

    // Sanitize text inputs
    const sanitizedPros = sanitizeText(reviewData.pros);
    const sanitizedCons = sanitizeText(reviewData.cons);
    const sanitizedAdvice = sanitizeText(reviewData.advice_management);

    // Check for potential PII in all text fields
    const piiWarnings: string[] = [];
    if (sanitizedPros) {
      const prosWarnings = detectPotentialPII(sanitizedPros);
      piiWarnings.push(...prosWarnings.map(w => `pros: ${w}`));
    }
    if (sanitizedCons) {
      const consWarnings = detectPotentialPII(sanitizedCons);
      piiWarnings.push(...consWarnings.map(w => `cons: ${w}`));
    }
    if (sanitizedAdvice) {
      const adviceWarnings = detectPotentialPII(sanitizedAdvice);
      piiWarnings.push(...adviceWarnings.map(w => `advice: ${w}`));
    }

    // Generate anonymous user hash for this user-company combination (RULE-601)
    const anonymousUserHash = generateAnonymousUserHash(currentUser.user_id, reviewData.company_id);

    // Insert review into content schema with anonymous hash (RULE-102, RULE-601)
    const { data: newReview, error: insertError } = await supabase
      .from('haq_content_db.reviews')
      .insert({
        company_id: reviewData.company_id,
        anonymous_user_hash: anonymousUserHash, // Anonymous hash instead of user ID
        overall_rating: reviewData.overall_rating,
        title: 'Anonymous Review', // Default title
        pros: sanitizedPros,
        cons: sanitizedCons,
        advice_to_management: sanitizedAdvice,
        is_approved: false // Default to pending admin approval
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error creating review:', insertError);
      return NextResponse.json(
        { 
          success: false, 
          message: 'Failed to submit review' 
        },
        { status: 500 }
      );
    }

    // Return success response with PII warnings if any
    const response: any = {
      success: true,
      message: 'Review submitted successfully and is pending moderation',
      data: {
        review_id: newReview.review_id,
        company_name: company.name,
        status: 'pending',
        submitted_at: newReview.created_at
      }
    };

    // Include PII warnings if detected
    if (piiWarnings.length > 0) {
      response.warnings = {
        pii_detected: piiWarnings,
        message: 'Potential personally identifiable information detected. Please review your submission.'
      };
    }

    return NextResponse.json(response, { status: 201 });

  } catch (error) {
    console.error('Review submission error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

/**
 * Handle unsupported HTTP methods
 */
export async function GET() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'POST' } }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'POST' } }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'POST' } }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'POST' } }
  );
}
