(()=>{var e={};e.id=634,e.ids=[634],e.modules={2770:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>l});var s=r(65239),a=r(48088),o=r(88170),n=r.n(o),i=r(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let l={children:["",{children:["test-search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,20284)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\test-search\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,82893)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\test-search\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-search/page",pathname:"/test-search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20284:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Haq website v1\\\\haq-frontend-nextjs\\\\src\\\\app\\\\test-search\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\test-search\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},52274:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(60687),a=r(43210),o=r(75877);function n(){let[e,t]=(0,a.useState)(""),[r,n]=(0,a.useState)([]);return(0,s.jsx)("div",{className:"min-h-screen bg-background-primary p-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-text-primary mb-8",children:"Search Functionality Test"}),(0,s.jsxs)("div",{className:"bg-surface-primary border border-border-primary rounded-lg p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"Test Search Bar"}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-text-primary mb-2",children:"Search with Auto-Navigate and Suggestions:"}),(0,s.jsx)(o.I,{value:e,onChange:t,placeholder:"Type 'tech' to test search...",autoNavigate:!0,showSuggestions:!0})]}),(0,s.jsxs)("div",{className:"text-sm text-text-secondary space-y-2",children:[(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:"Instructions:"})}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,s.jsx)("li",{children:'Type "tech" in the search bar'}),(0,s.jsx)("li",{children:"You should see suggestions dropdown after 2+ characters"}),(0,s.jsx)("li",{children:"Press Enter or click search button to navigate to search results"}),(0,s.jsx)("li",{children:"Click on a suggestion to go directly to that company"}),(0,s.jsx)("li",{children:"Check console logs below for debugging info"})]})]})]}),(0,s.jsxs)("div",{className:"bg-surface-primary border border-border-primary rounded-lg p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-text-primary",children:"Console Logs"}),(0,s.jsx)("button",{onClick:()=>{n([])},className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200",children:"Clear Logs"})]}),(0,s.jsx)("div",{className:"bg-surface-secondary border border-border-primary rounded-lg p-4 max-h-96 overflow-y-auto",children:0===r.length?(0,s.jsx)("p",{className:"text-text-secondary",children:"No logs yet. Try using the search bar above."}):(0,s.jsx)("div",{className:"space-y-1",children:r.map((e,t)=>(0,s.jsx)("div",{className:"text-sm text-text-primary font-mono",children:e},t))})})]}),(0,s.jsxs)("div",{className:"mt-6 bg-surface-primary border border-border-primary rounded-lg p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"Quick Tests"}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-text-primary mb-2",children:"Test API Endpoints:"}),(0,s.jsxs)("div",{className:"space-x-4",children:[(0,s.jsx)("button",{onClick:async()=>{try{let e=await fetch("/api/companies?q=tech&limit=3"),t=await e.json();console.log("Companies API Response:",t)}catch(e){console.error("Companies API Error:",e)}},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200",children:"Test Companies API"}),(0,s.jsx)("button",{onClick:async()=>{try{let e=await fetch("/api/search/companies?q=tech"),t=await e.json();console.log("Search API Response:",t)}catch(e){console.error("Search API Error:",e)}},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200",children:"Test Search API"})]})]})})]})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58971:(e,t,r)=>{Promise.resolve().then(r.bind(r,52274))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},75877:(e,t,r)=>{"use strict";r.d(t,{I:()=>d});var s=r(60687),a=r(43210),o=r(16189),n=r(99270),i=r(11860),c=r(85814),l=r.n(c);let d=({value:e,onChange:t,placeholder:r="Search companies...",onSubmit:c,autoNavigate:d=!1,showSuggestions:u=!1})=>{let m=(0,o.useRouter)(),[p,h]=(0,a.useState)([]),[x,b]=(0,a.useState)(!1),[g,f]=(0,a.useState)(!1),y=(0,a.useRef)(null);(0,a.useEffect)(()=>{if(!u||!e.trim()||e.length<2){h([]),b(!1);return}let t=setTimeout(async()=>{f(!0);try{let t=await fetch(`/api/companies?q=${encodeURIComponent(e.trim())}&limit=5`),r=await t.json();r.success&&r.data?.companies&&(h(r.data.companies),b(!0))}catch(e){console.error("Error fetching suggestions:",e),h([])}finally{f(!1)}},300);return()=>clearTimeout(t)},[e,u]),(0,a.useEffect)(()=>{let e=e=>{y.current&&!y.current.contains(e.target)&&b(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let v=e=>{b(!1),m.push(`/companies/${e.company_id}`)};return(0,s.jsxs)("div",{ref:y,className:"relative w-full",children:[(0,s.jsxs)("form",{onSubmit:t=>{if(t.preventDefault(),console.log("SearchBar: Form submitted with value:",e),console.log("SearchBar: autoNavigate:",d),b(!1),c){console.log("SearchBar: Using custom onSubmit"),c();return}if(d&&e.trim()){console.log("SearchBar: Navigating to search page...");let t=new URLSearchParams;t.set("q",e.trim());let r=`/search?${t.toString()}`;console.log("SearchBar: Navigation URL:",r),m.push(r)}else console.log("SearchBar: No navigation - autoNavigate:",d,"value:",e)},className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(n.A,{className:"h-5 w-5 text-text-secondary"})}),(0,s.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),className:"block w-full pl-10 pr-20 py-3 border border-border-primary rounded-medium bg-surface-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary text-text-primary transition-all duration-200",placeholder:r,autoComplete:"off"}),e&&(0,s.jsx)("button",{type:"button",onClick:()=>{t(""),b(!1),h([])},className:"absolute inset-y-0 right-12 flex items-center text-text-secondary hover:text-text-primary transition-colors duration-200","aria-label":"Clear search",children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}),(0,s.jsx)("button",{type:"submit",className:"absolute inset-y-0 right-0 flex items-center justify-center w-12 bg-accent-primary hover:bg-accent-secondary text-text-on-accent rounded-r-medium transition-colors duration-200","aria-label":"Search",onClick:e=>{console.log("SearchBar: Button clicked")},children:(0,s.jsx)(n.A,{className:"h-5 w-5"})})]}),u&&x&&(0,s.jsxs)("div",{className:"absolute top-full left-0 right-0 mt-1 bg-surface-primary border border-border-primary rounded-medium shadow-lg z-50 max-h-80 overflow-y-auto",children:[g&&(0,s.jsxs)("div",{className:"p-4 text-center text-text-secondary",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-accent-primary mx-auto"}),(0,s.jsx)("span",{className:"ml-2",children:"Searching..."})]}),!g&&0===p.length&&e.trim().length>=2&&(0,s.jsxs)("div",{className:"p-4 text-center text-text-secondary",children:['No companies found for "',e,'"']}),!g&&p.length>0&&(0,s.jsxs)(s.Fragment,{children:[p.map(e=>(0,s.jsxs)("button",{onClick:()=>v(e),className:"w-full text-left p-3 hover:bg-surface-secondary transition-colors duration-200 border-b border-border-primary last:border-b-0",children:[(0,s.jsx)("div",{className:"font-medium text-text-primary",children:e.name}),(0,s.jsxs)("div",{className:"text-sm text-text-secondary",children:[e.industry," • ",e.location]})]},e.company_id)),(0,s.jsxs)(l(),{href:`/search?q=${encodeURIComponent(e.trim())}`,className:"block w-full text-center p-3 text-accent-primary hover:bg-surface-secondary transition-colors duration-200 font-medium",onClick:()=>b(!1),children:['View all results for "',e,'"']})]})]})]})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},93315:(e,t,r)=>{Promise.resolve().then(r.bind(r,20284))},94735:e=>{"use strict";e.exports=require("events")},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,942,658,647],()=>r(2770));module.exports=s})();