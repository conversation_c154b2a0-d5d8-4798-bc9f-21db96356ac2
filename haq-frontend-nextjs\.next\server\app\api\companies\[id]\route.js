(()=>{var e={};e.id=127,e.ids=[127],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15510:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>g,serverHooks:()=>h,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{DELETE:()=>m,GET:()=>p,PATCH:()=>x,POST:()=>d,PUT:()=>l});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),u=r(56621),c=r(77882);async function p(e,{params:t}){try{let e=t.id;if(!c.ne.isValidUUID(e))return i.NextResponse.json({success:!1,message:"Invalid company ID format",error:"Company ID must be a valid UUID"},{status:400});let{data:r,error:s}=await u.ND.from("haq_content_db.companies").select(`
        company_id,
        name,
        slug,
        industry,
        location,
        website_url,
        logo_url,
        employee_count_range,
        founded_year,
        description,
        haq_score,
        total_reviews,
        is_verified,
        created_at,
        updated_at
      `).eq("company_id",e).single();if(s||!r)return i.NextResponse.json({success:!1,message:"Company not found",error:"The requested company does not exist"},{status:404});let{data:a,error:n}=await u.ND.from("haq_content_db.reviews").select("overall_rating").eq("company_id",e).eq("is_approved",!0),o={total_reviews:0,average_rating:0,rating_distribution:{1:0,2:0,3:0,4:0,5:0}};if(a&&a.length>0){o.total_reviews=a.length;let e=a.reduce((e,t)=>e+t.overall_rating,0);o.average_rating=Math.round(e/a.length*10)/10,a.forEach(e=>{o.rating_distribution[e.overall_rating]++})}let p={company:{...r,total_reviews:o.total_reviews,average_rating:o.average_rating,rating_distribution:o.rating_distribution}};return i.NextResponse.json({success:!0,data:p},{headers:{"Cache-Control":"public, max-age=300, s-maxage=300",Vary:"Accept-Encoding","X-Content-Type-Options":"nosniff","X-Frame-Options":"DENY"}})}catch(e){return console.error("Company details API error:",e),i.NextResponse.json({success:!1,message:"Internal server error",error:"Unexpected error occurred"},{status:500})}}async function d(){return i.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function l(){return i.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function m(){return i.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function x(){return i.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}let g=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/companies/[id]/route",pathname:"/api/companies/[id]",filename:"route",bundlePath:"app/api/companies/[id]/route"},resolvedPagePath:"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:_,workUnitAsyncStorage:v,serverHooks:h}=g;function y(){return(0,o.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:v})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32325:e=>{"use strict";e.exports=require("jsdom")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{ND:()=>n});var s=r(39398);r(98766);let a=null,n=a=(0,s.createClient)("https://wqbuilazpyxpwyuwuqpi.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndxYnVpbGF6cHl4cHd5dXd1cXBpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1NTYyNDMsImV4cCI6MjA2NjEzMjI0M30.GeRI54Rskwdbfm9_lRxy1-7YQ8vA74JWblNF2GRpzrI")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77882:(e,t,r)=>{"use strict";let s;r.d(t,{ne:()=>o});var a=r(39296);{let{JSDOM:e}=r(32325),t=new e("").window;s=(0,a.A)(t)}let n={BASIC_TEXT:{ALLOWED_TAGS:[],ALLOWED_ATTR:[],KEEP_CONTENT:!0,RETURN_DOM:!1}},o={isValidUUID:e=>/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(e),isValidRating:e=>Number.isInteger(e)&&e>=1&&e<=5,isValidTextLength:(e,t=5e3)=>"string"==typeof e&&e.length<=t}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,573,296],()=>r(15510));module.exports=s})();