(()=>{var e={};e.id=54,e.ids=[54],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6418:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>A,routeModule:()=>f,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>_});var r={};s.r(r),s.d(r,{DELETE:()=>y,GET:()=>m,PATCH:()=>h,POST:()=>w,PUT:()=>v});var a=s(96559),n=s(48088),i=s(37719),o=s(32190),u=s(23633),c=s(56621),d=s(45697);let l=d.z.object({status:d.z.enum(["approved","rejected"],{errorMap:()=>({message:'Status must be either "approved" or "rejected"'})}),reason:d.z.string().optional().transform(e=>e?.trim()||void 0)});async function p(e,t,s){try{let s,r=t.params.id;if(!r||"string"!=typeof r)return o.NextResponse.json({success:!1,message:"Invalid review ID format"},{status:400});try{s=await e.json()}catch(e){return o.NextResponse.json({success:!1,message:"Invalid JSON in request body"},{status:400})}let a=l.safeParse(s);if(!a.success)return o.NextResponse.json({success:!1,message:"Validation failed",errors:a.error.errors.map(e=>({field:e.path.join("."),message:e.message}))},{status:400});let{status:n,reason:i}=a.data,{data:u,error:d}=await c.ND.from("haq_content_db.reviews").select("review_id, company_id, is_approved").eq("review_id",r).single();if(d||!u)return o.NextResponse.json({success:!1,message:"Review not found"},{status:404});let{data:p,error:h}=await c.ND.from("haq_content_db.companies").select("company_id, name").eq("company_id",u.company_id).single();if(h||!p)return o.NextResponse.json({success:!1,message:"Company not found for this review"},{status:404});if(!0===u.is_approved)return o.NextResponse.json({success:!1,message:"Review is already approved. Only pending reviews can be moderated."},{status:400});let m={is_approved:"approved"===n,updated_at:new Date().toISOString()},{data:w,error:v}=await c.ND.from("haq_content_db.reviews").update(m).eq("review_id",r).select("review_id, company_id, is_approved, updated_at").single();if(v)return console.error("Error updating review status:",v),o.NextResponse.json({success:!1,message:"Failed to update review status"},{status:500});return console.info(`Review ${r} ${n} for company ${p.name}`),o.NextResponse.json({success:!0,message:`Review ${n} successfully`,data:{review_id:w.review_id,company:{company_id:p.company_id,name:p.name},is_approved:w.is_approved,updated_at:w.updated_at}},{headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0","X-Content-Type-Options":"nosniff","X-Frame-Options":"DENY"}})}catch(e){return console.error("Admin review status update error:",e),o.NextResponse.json({success:!1,message:"Internal server error"},{status:500})}}let h=(0,u.ix)(p);async function m(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"PATCH"}})}async function w(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"PATCH"}})}async function v(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"PATCH"}})}async function y(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"PATCH"}})}let f=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/reviews/[id]/status/route",pathname:"/api/admin/reviews/[id]/status",filename:"route",bundlePath:"app/api/admin/reviews/[id]/status/route"},resolvedPagePath:"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\admin\\reviews\\[id]\\status\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:g,workUnitAsyncStorage:_,serverHooks:x}=f;function A(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:_})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12909:(e,t,s)=>{"use strict";s.d(t,{DU:()=>c,Qi:()=>d,h8:()=>u,ne:()=>l});var r=s(85663),a=s(43205),n=s.n(a);let i=()=>{let e=process.env.JWT_SECRET;if(!e)throw Error("JWT_SECRET environment variable is required");return e},o=process.env.JWT_EXPIRES_IN||"7d";class u{static{this.SALT_ROUNDS=12}static async hashPassword(e){try{let t=await r.Ay.genSalt(this.SALT_ROUNDS);return await r.Ay.hash(e,t)}catch(e){throw Error("Failed to hash password")}}static async verifyPassword(e,t){try{return await r.Ay.compare(e,t)}catch(e){throw Error("Failed to verify password")}}}class c{static generateToken(e){try{return n().sign(e,i(),{expiresIn:o,algorithm:"HS256"})}catch(e){throw Error("Failed to generate JWT token")}}static verifyToken(e){try{return n().verify(e,i(),{algorithms:["HS256"]})}catch(e){if(e instanceof n().TokenExpiredError)throw Error("Token has expired");if(e instanceof n().JsonWebTokenError)throw Error("Invalid token");throw Error("Token verification failed")}}static extractTokenFromHeader(e){return e&&e.startsWith("Bearer ")?e.substring(7):null}}let d={NAME:"haq_auth_token",OPTIONS:{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:604800,path:"/"}};class l{static isValidEmail(e){return/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)&&e.length<=255}static isValidUsername(e){return/^[a-zA-Z0-9_]{3,50}$/.test(e)}static validatePassword(e){let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),e.length>128&&t.push("Password must be less than 128 characters"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[0-9]/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e)||t.push("Password must contain at least one special character"),{isValid:0===t.length,errors:t}}static sanitizeInput(e){return e.trim().replace(/[<>]/g,"").substring(0,1e3)}}},23633:(e,t,s)=>{"use strict";s.d(t,{ix:()=>i});var r=s(32190),a=s(51641);async function n(e){try{if(!await a.b.isAuthenticated())return{success:!1,error:"User is not authenticated",status:401};if(!await a.b.isAdmin())return{success:!1,error:"Unauthorized access: User does not have admin privileges",status:401};let e=await a.b.getCurrentUser();if(!e)return{success:!1,error:"Failed to retrieve user data",status:401};return{success:!0,user:e}}catch(e){return console.error("Admin auth verification error:",e),{success:!1,error:"Internal authentication error",status:500}}}function i(e){return async(t,s={})=>{let a=await n(t);if(!a.success)return r.NextResponse.json({success:!1,message:a.error||"Authentication failed"},{status:a.status||401});try{return await e(t,s,a.user)}catch(e){return console.error("Admin API handler error:",e),r.NextResponse.json({success:!1,message:"Internal server error"},{status:500})}}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51641:(e,t,s)=>{"use strict";s.d(t,{L:()=>i,b:()=>o});var r=s(44999),a=s(12909),n=s(56621);class i{static async setAuthCookie(e){(await (0,r.UL)()).set(a.Qi.NAME,e,a.Qi.OPTIONS)}static async getAuthToken(){try{let e=(await (0,r.UL)()).get(a.Qi.NAME);return e?.value||null}catch(e){return null}}static async removeAuthCookie(){(await (0,r.UL)()).delete(a.Qi.NAME)}}class o{static async getCurrentUser(){try{let e=await i.getAuthToken();if(!e)return null;let t=a.DU.verifyToken(e),{data:s,error:r}=await n.ND.from("haq_users_db.users").select("user_id, username, email, role, created_at, updated_at").eq("user_id",t.user_id).limit(1);if(r||!s||0===s.length)return null;return s[0]}catch(e){return null}}static async isAuthenticated(){return null!==await this.getCurrentUser()}static async isAdmin(){try{let e=await i.getAuthToken();if(!e)return!1;let t=a.DU.verifyToken(e);return"admin"===t.role}catch(e){return!1}}static async verifyTokenAndGetUser(e){try{let t=a.DU.verifyToken(e),{data:s,error:r}=await n.ND.from("haq_users_db.users").select("user_id, username, email, role, created_at, updated_at").eq("user_id",t.user_id).limit(1);if(r||!s||0===s.length)return null;return s[0]}catch(e){return null}}}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,s)=>{"use strict";s.d(t,{ND:()=>n});var r=s(39398);s(98766);let a=null,n=a=(0,r.createClient)("https://wqbuilazpyxpwyuwuqpi.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndxYnVpbGF6cHl4cHd5dXd1cXBpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1NTYyNDMsImV4cCI6MjA2NjEzMjI0M30.GeRI54Rskwdbfm9_lRxy1-7YQ8vA74JWblNF2GRpzrI")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580,573,358,697],()=>s(6418));module.exports=r})();