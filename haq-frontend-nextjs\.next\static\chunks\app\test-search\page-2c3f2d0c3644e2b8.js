(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[634],{741:(e,t,r)=>{"use strict";r.d(t,{I:()=>d});var s=r(5155),a=r(2115),o=r(5695),n=r(7924),c=r(4416),i=r(6874),l=r.n(i);let d=e=>{let{value:t,onChange:r,placeholder:i="Search companies...",onSubmit:d,autoNavigate:m=!1,showSuggestions:u=!1}=e,h=(0,o.useRouter)(),[x,p]=(0,a.useState)([]),[g,b]=(0,a.useState)(!1),[y,f]=(0,a.useState)(!1),v=(0,a.useRef)(null);(0,a.useEffect)(()=>{if(!u||!t.trim()||t.length<2){p([]),b(!1);return}let e=setTimeout(async()=>{f(!0);try{var e;let r=await fetch("/api/companies?q=".concat(encodeURIComponent(t.trim()),"&limit=5")),s=await r.json();s.success&&(null==(e=s.data)?void 0:e.companies)&&(p(s.data.companies),b(!0))}catch(e){console.error("Error fetching suggestions:",e),p([])}finally{f(!1)}},300);return()=>clearTimeout(e)},[t,u]),(0,a.useEffect)(()=>{let e=e=>{v.current&&!v.current.contains(e.target)&&b(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let j=e=>{b(!1),h.push("/companies/".concat(e.company_id))};return(0,s.jsxs)("div",{ref:v,className:"relative w-full",children:[(0,s.jsxs)("form",{onSubmit:e=>{if(e.preventDefault(),console.log("SearchBar: Form submitted with value:",t),console.log("SearchBar: autoNavigate:",m),b(!1),d){console.log("SearchBar: Using custom onSubmit"),d();return}if(m&&t.trim()){console.log("SearchBar: Navigating to search page...");let e=new URLSearchParams;e.set("q",t.trim());let r="/search?".concat(e.toString());console.log("SearchBar: Navigation URL:",r),h.push(r)}else console.log("SearchBar: No navigation - autoNavigate:",m,"value:",t)},className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(n.A,{className:"h-5 w-5 text-text-secondary"})}),(0,s.jsx)("input",{type:"text",value:t,onChange:e=>r(e.target.value),className:"block w-full pl-10 pr-20 py-3 border border-border-primary rounded-medium bg-surface-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-accent-primary text-text-primary transition-all duration-200",placeholder:i,autoComplete:"off"}),t&&(0,s.jsx)("button",{type:"button",onClick:()=>{r(""),b(!1),p([])},className:"absolute inset-y-0 right-12 flex items-center text-text-secondary hover:text-text-primary transition-colors duration-200","aria-label":"Clear search",children:(0,s.jsx)(c.A,{className:"h-4 w-4"})}),(0,s.jsx)("button",{type:"submit",className:"absolute inset-y-0 right-0 flex items-center justify-center w-12 bg-accent-primary hover:bg-accent-secondary text-text-on-accent rounded-r-medium transition-colors duration-200","aria-label":"Search",onClick:e=>{console.log("SearchBar: Button clicked")},children:(0,s.jsx)(n.A,{className:"h-5 w-5"})})]}),u&&g&&(0,s.jsxs)("div",{className:"absolute top-full left-0 right-0 mt-1 bg-surface-primary border border-border-primary rounded-medium shadow-lg z-50 max-h-80 overflow-y-auto",children:[y&&(0,s.jsxs)("div",{className:"p-4 text-center text-text-secondary",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-accent-primary mx-auto"}),(0,s.jsx)("span",{className:"ml-2",children:"Searching..."})]}),!y&&0===x.length&&t.trim().length>=2&&(0,s.jsxs)("div",{className:"p-4 text-center text-text-secondary",children:['No companies found for "',t,'"']}),!y&&x.length>0&&(0,s.jsxs)(s.Fragment,{children:[x.map(e=>(0,s.jsxs)("button",{onClick:()=>j(e),className:"w-full text-left p-3 hover:bg-surface-secondary transition-colors duration-200 border-b border-border-primary last:border-b-0",children:[(0,s.jsx)("div",{className:"font-medium text-text-primary",children:e.name}),(0,s.jsxs)("div",{className:"text-sm text-text-secondary",children:[e.industry," • ",e.location]})]},e.company_id)),(0,s.jsxs)(l(),{href:"/search?q=".concat(encodeURIComponent(t.trim())),className:"block w-full text-center p-3 text-accent-primary hover:bg-surface-secondary transition-colors duration-200 font-medium",onClick:()=>b(!1),children:['View all results for "',t,'"']})]})]})]})}},1781:(e,t,r)=>{Promise.resolve().then(r.bind(r,9300))},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},7924:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9300:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(5155),a=r(2115),o=r(741);function n(){let[e,t]=(0,a.useState)(""),[r,n]=(0,a.useState)([]);return a.useEffect(()=>{let e=console.log;return console.log=function(){for(var t=arguments.length,r=Array(t),s=0;s<t;s++)r[s]=arguments[s];e(...r),n(e=>[...e,r.join(" ")])},()=>{console.log=e}},[]),(0,s.jsx)("div",{className:"min-h-screen bg-background-primary p-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-text-primary mb-8",children:"Search Functionality Test"}),(0,s.jsxs)("div",{className:"bg-surface-primary border border-border-primary rounded-lg p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"Test Search Bar"}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-text-primary mb-2",children:"Search with Auto-Navigate and Suggestions:"}),(0,s.jsx)(o.I,{value:e,onChange:t,placeholder:"Type 'tech' to test search...",autoNavigate:!0,showSuggestions:!0})]}),(0,s.jsxs)("div",{className:"text-sm text-text-secondary space-y-2",children:[(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:"Instructions:"})}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,s.jsx)("li",{children:'Type "tech" in the search bar'}),(0,s.jsx)("li",{children:"You should see suggestions dropdown after 2+ characters"}),(0,s.jsx)("li",{children:"Press Enter or click search button to navigate to search results"}),(0,s.jsx)("li",{children:"Click on a suggestion to go directly to that company"}),(0,s.jsx)("li",{children:"Check console logs below for debugging info"})]})]})]}),(0,s.jsxs)("div",{className:"bg-surface-primary border border-border-primary rounded-lg p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-text-primary",children:"Console Logs"}),(0,s.jsx)("button",{onClick:()=>{n([])},className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200",children:"Clear Logs"})]}),(0,s.jsx)("div",{className:"bg-surface-secondary border border-border-primary rounded-lg p-4 max-h-96 overflow-y-auto",children:0===r.length?(0,s.jsx)("p",{className:"text-text-secondary",children:"No logs yet. Try using the search bar above."}):(0,s.jsx)("div",{className:"space-y-1",children:r.map((e,t)=>(0,s.jsx)("div",{className:"text-sm text-text-primary font-mono",children:e},t))})})]}),(0,s.jsxs)("div",{className:"mt-6 bg-surface-primary border border-border-primary rounded-lg p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"Quick Tests"}),(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium text-text-primary mb-2",children:"Test API Endpoints:"}),(0,s.jsxs)("div",{className:"space-x-4",children:[(0,s.jsx)("button",{onClick:async()=>{try{let e=await fetch("/api/companies?q=tech&limit=3"),t=await e.json();console.log("Companies API Response:",t)}catch(e){console.error("Companies API Error:",e)}},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200",children:"Test Companies API"}),(0,s.jsx)("button",{onClick:async()=>{try{let e=await fetch("/api/search/companies?q=tech"),t=await e.json();console.log("Search API Response:",t)}catch(e){console.error("Search API Error:",e)}},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200",children:"Test Search API"})]})]})})]})]})})}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var s=r(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},c=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},i=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:o=2,absoluteStrokeWidth:n,className:d="",children:m,iconNode:u,...h}=e;return(0,s.createElement)("svg",{ref:t,...l,width:a,height:a,stroke:r,strokeWidth:n?24*Number(o)/Number(a):o,className:c("lucide",d),...!m&&!i(h)&&{"aria-hidden":"true"},...h},[...u.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let r=(0,s.forwardRef)((r,o)=>{let{className:i,...l}=r;return(0,s.createElement)(d,{ref:o,iconNode:t,className:c("lucide-".concat(a(n(e))),"lucide-".concat(e),i),...l})});return r.displayName=n(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[244,441,684,358],()=>t(1781)),_N_E=e.O()}]);