(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[198],{646:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1243:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},3904:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4861:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},7105:(e,t,s)=>{Promise.resolve().then(s.bind(s,8216))},8216:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(5155),r=s(2115),i=s(3904),n=s(646),c=s(4861),d=s(1243);function l(){let[e,t]=(0,r.useState)([]),[s,l]=(0,r.useState)(!1),o=async()=>{l(!0),t([]);let e=[];for(let t of[{name:"Admin Login Test",test:async()=>{let e=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:"admin",password:"admin123"})});return{success:e.ok,data:await e.json()}}},{name:"Pending Reviews API Test",test:async()=>{let e=await fetch("/api/admin/reviews/pending?page=1&limit=5");return{success:e.ok,data:await e.json()}}},{name:"Unauthorized Access Test",test:async()=>{document.cookie.split(";").forEach(function(e){document.cookie=e.replace(/^ +/,"").replace(/=.*/,"=;expires="+new Date().toUTCString()+";path=/")});let e=await fetch("/api/admin/reviews/pending");return{success:401===e.status,data:await e.json()}}},{name:"Invalid Review ID Test",test:async()=>{let e=await fetch("/api/admin/reviews/invalid-id/status",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:"approved"})});return{success:404===e.status||400===e.status,data:await e.json()}}},{name:"Invalid Status Test",test:async()=>{let e=await fetch("/api/admin/reviews/test-id/status",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:"invalid_status"})});return{success:400===e.status,data:await e.json()}}}])try{console.log("Running ".concat(t.name,"..."));let s=await t.test();e.push({name:t.name,success:s.success,data:s.data,error:null})}catch(s){e.push({name:t.name,success:!1,data:null,error:s.message})}t(e),l(!1)};return(0,a.jsx)("div",{className:"min-h-screen bg-background-primary p-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-text-primary mb-8",children:"Admin Moderation Testing"}),(0,a.jsxs)("div",{className:"bg-surface-primary border border-border-primary rounded-lg p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"API Tests"}),(0,a.jsx)("div",{className:"flex space-x-4 mb-6",children:(0,a.jsxs)("button",{onClick:o,disabled:s,className:"bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-6 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50 flex items-center space-x-2",children:[s&&(0,a.jsx)(i.A,{className:"w-4 h-4 animate-spin"}),(0,a.jsx)("span",{children:"Run API Tests"})]})}),e.length>0&&(0,a.jsx)("div",{className:"space-y-3",children:e.map((e,t)=>(0,a.jsxs)("div",{className:"p-4 rounded-lg border ".concat(e.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[e.success?(0,a.jsx)(n.A,{className:"w-5 h-5 text-green-600"}):(0,a.jsx)(c.A,{className:"w-5 h-5 text-red-600"}),(0,a.jsx)("span",{className:"font-medium ".concat(e.success?"text-green-800":"text-red-800"),children:e.name})]}),e.error&&(0,a.jsxs)("p",{className:"mt-2 text-sm text-red-600",children:["Error: ",e.error]}),e.data&&(0,a.jsxs)("details",{className:"mt-2",children:[(0,a.jsx)("summary",{className:"text-sm text-gray-600 cursor-pointer",children:"View Response"}),(0,a.jsx)("pre",{className:"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto",children:JSON.stringify(e.data,null,2)})]})]},t))})]}),(0,a.jsxs)("div",{className:"bg-surface-primary border border-border-primary rounded-lg p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"UI Tests"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-text-primary mb-2",children:"Admin Dashboard"}),(0,a.jsx)("button",{onClick:()=>{window.open("/admin","_blank")},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200",children:"Open Admin Dashboard"}),(0,a.jsx)("p",{className:"text-sm text-text-secondary mt-2",children:"Test: Navigate to admin dashboard and verify moderation queue link"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-text-primary mb-2",children:"Moderation Queue"}),(0,a.jsx)("button",{onClick:()=>{window.open("/admin/moderation","_blank")},className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200",children:"Open Moderation Queue"}),(0,a.jsx)("p",{className:"text-sm text-text-secondary mt-2",children:"Test: Open moderation queue and verify pending reviews display"})]})]})]}),(0,a.jsxs)("div",{className:"bg-surface-primary border border-border-primary rounded-lg p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"Manual Testing Checklist"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("input",{type:"checkbox",className:"mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-text-primary",children:"Admin Authentication"}),(0,a.jsx)("p",{className:"text-sm text-text-secondary",children:"Verify only admin users can access moderation queue"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("input",{type:"checkbox",className:"mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-text-primary",children:"Pending Reviews Display"}),(0,a.jsx)("p",{className:"text-sm text-text-secondary",children:"Check that pending reviews are displayed with all required information"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("input",{type:"checkbox",className:"mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-text-primary",children:"Approve Functionality"}),(0,a.jsx)("p",{className:"text-sm text-text-secondary",children:"Test approve button with confirmation dialog"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("input",{type:"checkbox",className:"mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-text-primary",children:"Reject Functionality"}),(0,a.jsx)("p",{className:"text-sm text-text-secondary",children:"Test reject button with confirmation dialog"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("input",{type:"checkbox",className:"mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-text-primary",children:"Pagination"}),(0,a.jsx)("p",{className:"text-sm text-text-secondary",children:"Verify pagination works correctly for multiple pages"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("input",{type:"checkbox",className:"mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-text-primary",children:"Error Handling"}),(0,a.jsx)("p",{className:"text-sm text-text-secondary",children:"Test error scenarios and verify proper error messages"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("input",{type:"checkbox",className:"mt-1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-text-primary",children:"Real-time Updates"}),(0,a.jsx)("p",{className:"text-sm text-text-secondary",children:"Verify reviews disappear from queue after approval/rejection"})]})]})]})]}),(0,a.jsx)("div",{className:"mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(d.A,{className:"w-5 h-5 text-yellow-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-yellow-800",children:"Testing Instructions"}),(0,a.jsxs)("p",{className:"text-sm text-yellow-700 mt-1",children:["1. Make sure the dev server is running on localhost:3000",(0,a.jsx)("br",{}),"2. Ensure you have admin credentials (username: admin, password: admin123)",(0,a.jsx)("br",{}),"3. Create some pending reviews first using the review submission form",(0,a.jsx)("br",{}),"4. Run API tests to verify backend functionality",(0,a.jsx)("br",{}),"5. Use UI tests to verify frontend functionality",(0,a.jsx)("br",{}),"6. Complete manual testing checklist"]})]})]})})]})})}},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var a=s(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},c=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,t)=>{let{color:s="currentColor",size:r=24,strokeWidth:i=2,absoluteStrokeWidth:n,className:o="",children:m,iconNode:x,...p}=e;return(0,a.createElement)("svg",{ref:t,...l,width:r,height:r,stroke:s,strokeWidth:n?24*Number(i)/Number(r):i,className:c("lucide",o),...!m&&!d(p)&&{"aria-hidden":"true"},...p},[...x.map(e=>{let[t,s]=e;return(0,a.createElement)(t,s)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let s=(0,a.forwardRef)((s,i)=>{let{className:d,...l}=s;return(0,a.createElement)(o,{ref:i,iconNode:t,className:c("lucide-".concat(r(n(e))),"lucide-".concat(e),d),...l})});return s.displayName=n(e),s}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(7105)),_N_E=e.O()}]);