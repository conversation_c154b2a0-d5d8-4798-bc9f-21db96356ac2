[{"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\companies\\page.tsx": "1", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\login\\page.tsx": "2", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\page.tsx": "3", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\admin\\companies\\route.ts": "4", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\login\\route.ts": "5", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\logout\\route.ts": "6", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\me\\route.ts": "7", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\register\\route.ts": "8", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\route.ts": "9", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\[id]\\reviews\\route.ts": "10", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\[id]\\route.ts": "11", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\reviews\\route.ts": "12", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\search\\companies\\route.ts": "13", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\auth\\login\\page.tsx": "14", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\auth\\signup\\page.tsx": "15", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\companies\\page.tsx": "16", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\companies\\[companyId]\\page.tsx": "17", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\layout.tsx": "18", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\page.tsx": "19", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\review\\submit\\page.tsx": "20", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\search\\page.tsx": "21", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\admin\\AdminLayout.tsx": "22", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\ClientOnly.tsx": "23", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\CompanyCard.tsx": "24", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\ErrorBoundary.tsx": "25", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\SearchBar.tsx": "26", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\companies\\CompaniesListClient.tsx": "27", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\companies\\CompanyDetails.tsx": "28", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\companies\\CompanyReviews.tsx": "29", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\layout\\Footer.tsx": "30", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\layout\\Header.tsx": "31", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\reviews\\ReviewForm.tsx": "32", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\contexts\\AuthContext.tsx": "33", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\admin-middleware.ts": "34", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\auth-client.ts": "35", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\auth-server.ts": "36", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\auth.ts": "37", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\sanitization.ts": "38", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\supabase.ts": "39", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\providers\\SWRProvider.tsx": "40", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\moderation\\page.tsx": "41", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\admin\\reviews\\pending\\route.ts": "42", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\admin\\reviews\\[id]\\status\\route.ts": "43", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\debug-search\\page.tsx": "44", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\test-moderation\\page.tsx": "45", "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\test-search\\page.tsx": "46"}, {"size": 15958, "mtime": 1750565922723, "results": "47", "hashOfConfig": "48"}, {"size": 7814, "mtime": 1750565827334, "results": "49", "hashOfConfig": "48"}, {"size": 8300, "mtime": 1750732446778, "results": "50", "hashOfConfig": "48"}, {"size": 6310, "mtime": 1750735540700, "results": "51", "hashOfConfig": "48"}, {"size": 5063, "mtime": 1750734635898, "results": "52", "hashOfConfig": "48"}, {"size": 1549, "mtime": 1750734253707, "results": "53", "hashOfConfig": "48"}, {"size": 3058, "mtime": 1750734648565, "results": "54", "hashOfConfig": "48"}, {"size": 5522, "mtime": 1750564667246, "results": "55", "hashOfConfig": "48"}, {"size": 4015, "mtime": 1750734690094, "results": "56", "hashOfConfig": "48"}, {"size": 6918, "mtime": 1750734770788, "results": "57", "hashOfConfig": "48"}, {"size": 4571, "mtime": 1750734718124, "results": "58", "hashOfConfig": "48"}, {"size": 7839, "mtime": 1750734855900, "results": "59", "hashOfConfig": "48"}, {"size": 4520, "mtime": 1750735029065, "results": "60", "hashOfConfig": "48"}, {"size": 11562, "mtime": 1750565118129, "results": "61", "hashOfConfig": "48"}, {"size": 15057, "mtime": 1750565099631, "results": "62", "hashOfConfig": "48"}, {"size": 1091, "mtime": 1750709259476, "results": "63", "hashOfConfig": "48"}, {"size": 4135, "mtime": 1750709346999, "results": "64", "hashOfConfig": "48"}, {"size": 1679, "mtime": 1750710564466, "results": "65", "hashOfConfig": "48"}, {"size": 12047, "mtime": 1750718035145, "results": "66", "hashOfConfig": "48"}, {"size": 755, "mtime": 1750708519360, "results": "67", "hashOfConfig": "48"}, {"size": 11663, "mtime": 1750716754885, "results": "68", "hashOfConfig": "48"}, {"size": 7036, "mtime": 1750732373142, "results": "69", "hashOfConfig": "48"}, {"size": 643, "mtime": 1750709844599, "results": "70", "hashOfConfig": "48"}, {"size": 7780, "mtime": 1750711033976, "results": "71", "hashOfConfig": "48"}, {"size": 1553, "mtime": 1750711011990, "results": "72", "hashOfConfig": "48"}, {"size": 7068, "mtime": 1750718115085, "results": "73", "hashOfConfig": "48"}, {"size": 10866, "mtime": 1750709303596, "results": "74", "hashOfConfig": "48"}, {"size": 8733, "mtime": 1750708957012, "results": "75", "hashOfConfig": "48"}, {"size": 11210, "mtime": 1750709004132, "results": "76", "hashOfConfig": "48"}, {"size": 4615, "mtime": 1750559093271, "results": "77", "hashOfConfig": "48"}, {"size": 12061, "mtime": 1750564544965, "results": "78", "hashOfConfig": "48"}, {"size": 29802, "mtime": 1750708152017, "results": "79", "hashOfConfig": "48"}, {"size": 8343, "mtime": 1750734296271, "results": "80", "hashOfConfig": "48"}, {"size": 4331, "mtime": 1750565705541, "results": "81", "hashOfConfig": "48"}, {"size": 3950, "mtime": 1750565091242, "results": "82", "hashOfConfig": "48"}, {"size": 3416, "mtime": 1750734675141, "results": "83", "hashOfConfig": "48"}, {"size": 6410, "mtime": 1750565064189, "results": "84", "hashOfConfig": "48"}, {"size": 7351, "mtime": 1750707548536, "results": "85", "hashOfConfig": "48"}, {"size": 5613, "mtime": 1750710248393, "results": "86", "hashOfConfig": "48"}, {"size": 3640, "mtime": 1750563028273, "results": "87", "hashOfConfig": "48"}, {"size": 17966, "mtime": 1750733223950, "results": "88", "hashOfConfig": "48"}, {"size": 6479, "mtime": 1750734932073, "results": "89", "hashOfConfig": "48"}, {"size": 6328, "mtime": 1750735012986, "results": "90", "hashOfConfig": "48"}, {"size": 4207, "mtime": 1750716809550, "results": "91", "hashOfConfig": "48"}, {"size": 11347, "mtime": 1750732552643, "results": "92", "hashOfConfig": "48"}, {"size": 4966, "mtime": 1750718149287, "results": "93", "hashOfConfig": "48"}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "d5o054", {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\companies\\page.tsx", ["232"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\login\\page.tsx", ["233"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\page.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\admin\\companies\\route.ts", ["234", "235", "236", "237", "238", "239", "240", "241", "242"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\login\\route.ts", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\logout\\route.ts", ["243", "244"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\me\\route.ts", ["245", "246"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\auth\\register\\route.ts", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\route.ts", ["247"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\[id]\\reviews\\route.ts", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\companies\\[id]\\route.ts", ["248", "249"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\reviews\\route.ts", ["250", "251"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\search\\companies\\route.ts", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\auth\\login\\page.tsx", ["252"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\auth\\signup\\page.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\companies\\page.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\companies\\[companyId]\\page.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\layout.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\page.tsx", ["253", "254", "255", "256", "257", "258"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\review\\submit\\page.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\search\\page.tsx", ["259", "260", "261", "262", "263"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\admin\\AdminLayout.tsx", ["264"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\ClientOnly.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\CompanyCard.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\ErrorBoundary.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\common\\SearchBar.tsx", ["265", "266", "267", "268", "269"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\companies\\CompaniesListClient.tsx", ["270", "271", "272"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\companies\\CompanyDetails.tsx", ["273"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\companies\\CompanyReviews.tsx", ["274", "275", "276", "277", "278"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\layout\\Footer.tsx", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\layout\\Header.tsx", ["279"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\components\\reviews\\ReviewForm.tsx", ["280", "281", "282", "283", "284", "285", "286"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\contexts\\AuthContext.tsx", ["287", "288", "289", "290", "291"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\admin-middleware.ts", ["292", "293", "294", "295", "296", "297"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\auth-client.ts", ["298", "299"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\auth-server.ts", ["300", "301", "302", "303", "304"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\auth.ts", ["305", "306", "307", "308", "309"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\sanitization.ts", ["310", "311", "312", "313", "314", "315", "316"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\lib\\supabase.ts", [], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\providers\\SWRProvider.tsx", ["317", "318"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\moderation\\page.tsx", ["319", "320"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\admin\\reviews\\pending\\route.ts", ["321", "322", "323", "324"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\admin\\reviews\\[id]\\status\\route.ts", ["325", "326", "327", "328", "329"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\debug-search\\page.tsx", ["330"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\test-moderation\\page.tsx", ["331"], [], "D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\test-search\\page.tsx", ["332", "333"], [], {"ruleId": "334", "severity": 1, "message": "335", "line": 192, "column": 29, "nodeType": "336", "endLine": 196, "endColumn": 31}, {"ruleId": "337", "severity": 2, "message": "338", "line": 52, "column": 14, "nodeType": null, "messageId": "339", "endLine": 52, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "340", "line": 23, "column": 51, "nodeType": null, "messageId": "339", "endLine": 23, "endColumn": 58}, {"ruleId": "341", "severity": 2, "message": "342", "line": 23, "column": 60, "nodeType": "343", "messageId": "344", "endLine": 23, "endColumn": 63, "suggestions": "345"}, {"ruleId": "337", "severity": 2, "message": "346", "line": 23, "column": 65, "nodeType": null, "messageId": "339", "endLine": 23, "endColumn": 69}, {"ruleId": "341", "severity": 2, "message": "342", "line": 23, "column": 71, "nodeType": "343", "messageId": "344", "endLine": 23, "endColumn": 74, "suggestions": "347"}, {"ruleId": "337", "severity": 2, "message": "348", "line": 70, "column": 37, "nodeType": null, "messageId": "339", "endLine": 70, "endColumn": 42}, {"ruleId": "337", "severity": 2, "message": "340", "line": 112, "column": 52, "nodeType": null, "messageId": "339", "endLine": 112, "endColumn": 59}, {"ruleId": "341", "severity": 2, "message": "342", "line": 112, "column": 61, "nodeType": "343", "messageId": "344", "endLine": 112, "endColumn": 64, "suggestions": "349"}, {"ruleId": "337", "severity": 2, "message": "346", "line": 112, "column": 66, "nodeType": null, "messageId": "339", "endLine": 112, "endColumn": 70}, {"ruleId": "341", "severity": 2, "message": "342", "line": 112, "column": 72, "nodeType": "343", "messageId": "344", "endLine": 112, "endColumn": 75, "suggestions": "350"}, {"ruleId": "337", "severity": 2, "message": "351", "line": 2, "column": 10, "nodeType": null, "messageId": "339", "endLine": 2, "endColumn": 18}, {"ruleId": "337", "severity": 2, "message": "352", "line": 10, "column": 28, "nodeType": null, "messageId": "339", "endLine": 10, "endColumn": 35}, {"ruleId": "337", "severity": 2, "message": "352", "line": 18, "column": 27, "nodeType": null, "messageId": "339", "endLine": 18, "endColumn": 34}, {"ruleId": "337", "severity": 2, "message": "338", "line": 34, "column": 14, "nodeType": null, "messageId": "339", "endLine": 34, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "348", "line": 52, "column": 37, "nodeType": null, "messageId": "339", "endLine": 52, "endColumn": 42}, {"ruleId": "337", "severity": 2, "message": "353", "line": 72, "column": 39, "nodeType": null, "messageId": "339", "endLine": 72, "endColumn": 49}, {"ruleId": "354", "severity": 2, "message": "355", "line": 79, "column": 9, "nodeType": "356", "messageId": "357", "endLine": 79, "endColumn": 25, "fix": "358"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 11, "column": 36, "nodeType": "343", "messageId": "344", "endLine": 11, "endColumn": 39, "suggestions": "359"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 196, "column": 21, "nodeType": "343", "messageId": "344", "endLine": 196, "endColumn": 24, "suggestions": "360"}, {"ruleId": "361", "severity": 2, "message": "362", "line": 282, "column": 18, "nodeType": "363", "messageId": "364", "suggestions": "365"}, {"ruleId": "337", "severity": 2, "message": "366", "line": 6, "column": 33, "nodeType": null, "messageId": "339", "endLine": 6, "endColumn": 37}, {"ruleId": "337", "severity": 2, "message": "367", "line": 6, "column": 66, "nodeType": null, "messageId": "339", "endLine": 6, "endColumn": 77}, {"ruleId": "337", "severity": 2, "message": "368", "line": 6, "column": 91, "nodeType": null, "messageId": "339", "endLine": 6, "endColumn": 104}, {"ruleId": "337", "severity": 2, "message": "369", "line": 6, "column": 106, "nodeType": null, "messageId": "339", "endLine": 6, "endColumn": 116}, {"ruleId": "337", "severity": 2, "message": "370", "line": 11, "column": 7, "nodeType": null, "messageId": "339", "endLine": 11, "endColumn": 24}, {"ruleId": "341", "severity": 2, "message": "342", "line": 237, "column": 51, "nodeType": "343", "messageId": "344", "endLine": 237, "endColumn": 54, "suggestions": "371"}, {"ruleId": "334", "severity": 1, "message": "335", "line": 48, "column": 13, "nodeType": "336", "endLine": 52, "endColumn": 15}, {"ruleId": "361", "severity": 2, "message": "372", "line": 201, "column": 38, "nodeType": "363", "messageId": "364", "suggestions": "373"}, {"ruleId": "361", "severity": 2, "message": "372", "line": 201, "column": 49, "nodeType": "363", "messageId": "364", "suggestions": "374"}, {"ruleId": "361", "severity": 2, "message": "372", "line": 236, "column": 52, "nodeType": "363", "messageId": "364", "suggestions": "375"}, {"ruleId": "361", "severity": 2, "message": "372", "line": 236, "column": 63, "nodeType": "363", "messageId": "364", "suggestions": "376"}, {"ruleId": "361", "severity": 2, "message": "362", "line": 83, "column": 52, "nodeType": "363", "messageId": "364", "suggestions": "377"}, {"ruleId": "337", "severity": 2, "message": "378", "line": 150, "column": 21, "nodeType": null, "messageId": "339", "endLine": 150, "endColumn": 22}, {"ruleId": "361", "severity": 2, "message": "372", "line": 171, "column": 38, "nodeType": "363", "messageId": "364", "suggestions": "379"}, {"ruleId": "361", "severity": 2, "message": "372", "line": 171, "column": 46, "nodeType": "363", "messageId": "364", "suggestions": "380"}, {"ruleId": "361", "severity": 2, "message": "372", "line": 196, "column": 38, "nodeType": "363", "messageId": "364", "suggestions": "381"}, {"ruleId": "361", "severity": 2, "message": "372", "line": 196, "column": 46, "nodeType": "363", "messageId": "364", "suggestions": "382"}, {"ruleId": "354", "severity": 2, "message": "383", "line": 115, "column": 9, "nodeType": "356", "messageId": "357", "endLine": 115, "endColumn": 16, "fix": "384"}, {"ruleId": "361", "severity": 2, "message": "372", "line": 228, "column": 28, "nodeType": "363", "messageId": "364", "suggestions": "385"}, {"ruleId": "361", "severity": 2, "message": "372", "line": 228, "column": 51, "nodeType": "363", "messageId": "364", "suggestions": "386"}, {"ruleId": "334", "severity": 1, "message": "335", "line": 122, "column": 15, "nodeType": "336", "endLine": 126, "endColumn": 17}, {"ruleId": "337", "severity": 2, "message": "387", "line": 3, "column": 27, "nodeType": null, "messageId": "339", "endLine": 3, "endColumn": 36}, {"ruleId": "337", "severity": 2, "message": "388", "line": 62, "column": 23, "nodeType": null, "messageId": "339", "endLine": 62, "endColumn": 37}, {"ruleId": "337", "severity": 2, "message": "389", "line": 63, "column": 10, "nodeType": null, "messageId": "339", "endLine": 63, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "390", "line": 63, "column": 21, "nodeType": null, "messageId": "339", "endLine": 63, "endColumn": 33}, {"ruleId": "354", "severity": 2, "message": "383", "line": 123, "column": 9, "nodeType": "356", "messageId": "357", "endLine": 123, "endColumn": 16, "fix": "391"}, {"ruleId": "337", "severity": 2, "message": "392", "line": 6, "column": 10, "nodeType": null, "messageId": "339", "endLine": 6, "endColumn": 16}, {"ruleId": "337", "severity": 2, "message": "393", "line": 49, "column": 11, "nodeType": null, "messageId": "339", "endLine": 49, "endColumn": 15}, {"ruleId": "337", "severity": 2, "message": "394", "line": 65, "column": 26, "nodeType": null, "messageId": "339", "endLine": 65, "endColumn": 33}, {"ruleId": "334", "severity": 1, "message": "335", "line": 349, "column": 29, "nodeType": "336", "endLine": 353, "endColumn": 31}, {"ruleId": "334", "severity": 1, "message": "335", "line": 400, "column": 19, "nodeType": "336", "endLine": 404, "endColumn": 21}, {"ruleId": "361", "severity": 2, "message": "362", "line": 713, "column": 65, "nodeType": "363", "messageId": "364", "suggestions": "395"}, {"ruleId": "361", "severity": 2, "message": "362", "line": 714, "column": 28, "nodeType": "363", "messageId": "364", "suggestions": "396"}, {"ruleId": "397", "severity": 2, "message": "398", "line": 778, "column": 15, "nodeType": "336", "endLine": 781, "endColumn": 16}, {"ruleId": "399", "severity": 1, "message": "400", "line": 97, "column": 6, "nodeType": "401", "endLine": 97, "endColumn": 8, "suggestions": "402"}, {"ruleId": "337", "severity": 2, "message": "338", "line": 120, "column": 14, "nodeType": null, "messageId": "339", "endLine": 120, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "338", "line": 151, "column": 14, "nodeType": null, "messageId": "339", "endLine": 151, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "338", "line": 181, "column": 14, "nodeType": null, "messageId": "339", "endLine": 181, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "338", "line": 201, "column": 14, "nodeType": null, "messageId": "339", "endLine": 201, "endColumn": 19}, {"ruleId": "341", "severity": 2, "message": "342", "line": 12, "column": 10, "nodeType": "343", "messageId": "344", "endLine": 12, "endColumn": 13, "suggestions": "403"}, {"ruleId": "337", "severity": 2, "message": "352", "line": 22, "column": 39, "nodeType": null, "messageId": "339", "endLine": 22, "endColumn": 46}, {"ruleId": "341", "severity": 2, "message": "342", "line": 74, "column": 44, "nodeType": "343", "messageId": "344", "endLine": 74, "endColumn": 47, "suggestions": "404"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 74, "column": 55, "nodeType": "343", "messageId": "344", "endLine": 74, "endColumn": 58, "suggestions": "405"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 76, "column": 48, "nodeType": "343", "messageId": "344", "endLine": 76, "endColumn": 51, "suggestions": "406"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 148, "column": 54, "nodeType": "343", "messageId": "344", "endLine": 148, "endColumn": 57, "suggestions": "407"}, {"ruleId": "337", "severity": 2, "message": "338", "line": 119, "column": 14, "nodeType": null, "messageId": "339", "endLine": 119, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "338", "line": 139, "column": 14, "nodeType": null, "messageId": "339", "endLine": 139, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "408", "line": 2, "column": 51, "nodeType": null, "messageId": "339", "endLine": 2, "endColumn": 61}, {"ruleId": "337", "severity": 2, "message": "338", "line": 25, "column": 14, "nodeType": null, "messageId": "339", "endLine": 25, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "338", "line": 66, "column": 14, "nodeType": null, "messageId": "339", "endLine": 66, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "338", "line": 93, "column": 14, "nodeType": null, "messageId": "339", "endLine": 93, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "338", "line": 119, "column": 14, "nodeType": null, "messageId": "339", "endLine": 119, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "338", "line": 53, "column": 14, "nodeType": null, "messageId": "339", "endLine": 53, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "338", "line": 67, "column": 14, "nodeType": null, "messageId": "339", "endLine": 67, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "338", "line": 86, "column": 14, "nodeType": null, "messageId": "339", "endLine": 86, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "338", "line": 222, "column": 14, "nodeType": null, "messageId": "339", "endLine": 222, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "338", "line": 236, "column": 14, "nodeType": null, "messageId": "339", "endLine": 236, "endColumn": 19}, {"ruleId": "409", "severity": 2, "message": "410", "line": 16, "column": 21, "nodeType": "411", "messageId": "412", "endLine": 16, "endColumn": 37}, {"ruleId": "341", "severity": 2, "message": "342", "line": 18, "column": 32, "nodeType": "343", "messageId": "344", "endLine": 18, "endColumn": 35, "suggestions": "413"}, {"ruleId": "337", "severity": 2, "message": "338", "line": 132, "column": 12, "nodeType": null, "messageId": "339", "endLine": 132, "endColumn": 17}, {"ruleId": "341", "severity": 2, "message": "342", "line": 141, "column": 57, "nodeType": "343", "messageId": "344", "endLine": 141, "endColumn": 60, "suggestions": "414"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 151, "column": 23, "nodeType": "343", "messageId": "344", "endLine": 151, "endColumn": 26, "suggestions": "415"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 155, "column": 21, "nodeType": "343", "messageId": "344", "endLine": 155, "endColumn": 24, "suggestions": "416"}, {"ruleId": "417", "severity": 1, "message": "418", "line": 277, "column": 1, "nodeType": "419", "endLine": 287, "endColumn": 3}, {"ruleId": "337", "severity": 2, "message": "420", "line": 8, "column": 9, "nodeType": null, "messageId": "339", "endLine": 8, "endColumn": 17}, {"ruleId": "341", "severity": 2, "message": "342", "line": 106, "column": 21, "nodeType": "343", "messageId": "344", "endLine": 106, "endColumn": 24, "suggestions": "421"}, {"ruleId": "337", "severity": 2, "message": "422", "line": 12, "column": 3, "nodeType": null, "messageId": "339", "endLine": 12, "endColumn": 11}, {"ruleId": "337", "severity": 2, "message": "423", "line": 259, "column": 9, "nodeType": null, "messageId": "339", "endLine": 259, "endColumn": 15}, {"ruleId": "337", "severity": 2, "message": "340", "line": 22, "column": 3, "nodeType": null, "messageId": "339", "endLine": 22, "endColumn": 10}, {"ruleId": "341", "severity": 2, "message": "342", "line": 22, "column": 12, "nodeType": "343", "messageId": "344", "endLine": 22, "endColumn": 15, "suggestions": "424"}, {"ruleId": "337", "severity": 2, "message": "346", "line": 23, "column": 3, "nodeType": null, "messageId": "339", "endLine": 23, "endColumn": 7}, {"ruleId": "341", "severity": 2, "message": "342", "line": 23, "column": 9, "nodeType": "343", "messageId": "344", "endLine": 23, "endColumn": 12, "suggestions": "425"}, {"ruleId": "337", "severity": 2, "message": "346", "line": 36, "column": 3, "nodeType": null, "messageId": "339", "endLine": 36, "endColumn": 7}, {"ruleId": "341", "severity": 2, "message": "342", "line": 36, "column": 9, "nodeType": "343", "messageId": "344", "endLine": 36, "endColumn": 12, "suggestions": "426"}, {"ruleId": "337", "severity": 2, "message": "338", "line": 56, "column": 14, "nodeType": null, "messageId": "339", "endLine": 56, "endColumn": 19}, {"ruleId": "337", "severity": 2, "message": "427", "line": 82, "column": 21, "nodeType": null, "messageId": "339", "endLine": 82, "endColumn": 27}, {"ruleId": "341", "severity": 2, "message": "342", "line": 130, "column": 23, "nodeType": "343", "messageId": "344", "endLine": 130, "endColumn": 26, "suggestions": "428"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 7, "column": 42, "nodeType": "343", "messageId": "344", "endLine": 7, "endColumn": 45, "suggestions": "429"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 7, "column": 50, "nodeType": "343", "messageId": "344", "endLine": 7, "endColumn": 53, "suggestions": "430"}, {"ruleId": "361", "severity": 2, "message": "372", "line": 51, "column": 24, "nodeType": "363", "messageId": "364", "suggestions": "431"}, {"ruleId": "361", "severity": 2, "message": "372", "line": 51, "column": 29, "nodeType": "363", "messageId": "364", "suggestions": "432"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", "'context' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["433", "434"], "'user' is defined but never used.", ["435", "436"], "'count' is assigned a value but never used.", ["437", "438"], ["439", "440"], "'JWTUtils' is defined but never used.", "'request' is defined but never used.", "'statsError' is assigned a value but never used.", "prefer-const", "'reviewStatistics' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "441", "text": "442"}, ["443", "444"], ["445", "446"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["447", "448", "449", "450"], "'Star' is defined but never used.", "'CheckCircle' is defined but never used.", "'MessageCircle' is defined but never used.", "'DollarSign' is defined but never used.", "'featuredCompanies' is assigned a value but never used.", ["451", "452"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["453", "454", "455", "456"], ["457", "458", "459", "460"], ["461", "462", "463", "464"], ["465", "466", "467", "468"], ["469", "470", "471", "472"], "'e' is defined but never used.", ["473", "474", "475", "476"], ["477", "478", "479", "480"], ["481", "482", "483", "484"], ["485", "486", "487", "488"], "'endPage' is never reassigned. Use 'const' instead.", {"range": "489", "text": "490"}, ["491", "492", "493", "494"], ["495", "496", "497", "498"], "'useEffect' is defined but never used.", "'setReviewsData' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", {"range": "499", "text": "490"}, "'Search' is defined but never used.", "'user' is assigned a value but never used.", "'isValid' is assigned a value but never used.", ["500", "501", "502", "503"], ["504", "505", "506", "507"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'checkExistingSession'. Either include it or remove the dependency array.", "ArrayExpression", ["508"], ["509", "510"], ["511", "512"], ["513", "514"], ["515", "516"], ["517", "518"], "'JWTPayload' is defined but never used.", "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["519", "520"], ["521", "522"], ["523", "524"], ["525", "526"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'supabase' is assigned a value but never used.", ["527", "528"], "'Calendar' is defined but never used.", "'router' is assigned a value but never used.", ["529", "530"], ["531", "532"], ["533", "534"], "'reason' is assigned a value but never used.", ["535", "536"], ["537", "538"], ["539", "540"], ["541", "542", "543", "544"], ["545", "546", "547", "548"], {"messageId": "549", "fix": "550", "desc": "551"}, {"messageId": "552", "fix": "553", "desc": "554"}, {"messageId": "549", "fix": "555", "desc": "551"}, {"messageId": "552", "fix": "556", "desc": "554"}, {"messageId": "549", "fix": "557", "desc": "551"}, {"messageId": "552", "fix": "558", "desc": "554"}, {"messageId": "549", "fix": "559", "desc": "551"}, {"messageId": "552", "fix": "560", "desc": "554"}, [2119, 2305], "const reviewStatistics = {\n      total_reviews: 0,\n      average_rating: 0,\n      rating_distribution: {\n        1: 0,\n        2: 0,\n        3: 0,\n        4: 0,\n        5: 0\n      }\n    };", {"messageId": "549", "fix": "561", "desc": "551"}, {"messageId": "552", "fix": "562", "desc": "554"}, {"messageId": "549", "fix": "563", "desc": "551"}, {"messageId": "552", "fix": "564", "desc": "554"}, {"messageId": "565", "data": "566", "fix": "567", "desc": "568"}, {"messageId": "565", "data": "569", "fix": "570", "desc": "571"}, {"messageId": "565", "data": "572", "fix": "573", "desc": "574"}, {"messageId": "565", "data": "575", "fix": "576", "desc": "577"}, {"messageId": "549", "fix": "578", "desc": "551"}, {"messageId": "552", "fix": "579", "desc": "554"}, {"messageId": "565", "data": "580", "fix": "581", "desc": "582"}, {"messageId": "565", "data": "583", "fix": "584", "desc": "585"}, {"messageId": "565", "data": "586", "fix": "587", "desc": "588"}, {"messageId": "565", "data": "589", "fix": "590", "desc": "591"}, {"messageId": "565", "data": "592", "fix": "593", "desc": "582"}, {"messageId": "565", "data": "594", "fix": "595", "desc": "585"}, {"messageId": "565", "data": "596", "fix": "597", "desc": "588"}, {"messageId": "565", "data": "598", "fix": "599", "desc": "591"}, {"messageId": "565", "data": "600", "fix": "601", "desc": "582"}, {"messageId": "565", "data": "602", "fix": "603", "desc": "585"}, {"messageId": "565", "data": "604", "fix": "605", "desc": "588"}, {"messageId": "565", "data": "606", "fix": "607", "desc": "591"}, {"messageId": "565", "data": "608", "fix": "609", "desc": "582"}, {"messageId": "565", "data": "610", "fix": "611", "desc": "585"}, {"messageId": "565", "data": "612", "fix": "613", "desc": "588"}, {"messageId": "565", "data": "614", "fix": "615", "desc": "591"}, {"messageId": "565", "data": "616", "fix": "617", "desc": "568"}, {"messageId": "565", "data": "618", "fix": "619", "desc": "571"}, {"messageId": "565", "data": "620", "fix": "621", "desc": "574"}, {"messageId": "565", "data": "622", "fix": "623", "desc": "577"}, {"messageId": "565", "data": "624", "fix": "625", "desc": "582"}, {"messageId": "565", "data": "626", "fix": "627", "desc": "585"}, {"messageId": "565", "data": "628", "fix": "629", "desc": "588"}, {"messageId": "565", "data": "630", "fix": "631", "desc": "591"}, {"messageId": "565", "data": "632", "fix": "633", "desc": "582"}, {"messageId": "565", "data": "634", "fix": "635", "desc": "585"}, {"messageId": "565", "data": "636", "fix": "637", "desc": "588"}, {"messageId": "565", "data": "638", "fix": "639", "desc": "591"}, {"messageId": "565", "data": "640", "fix": "641", "desc": "582"}, {"messageId": "565", "data": "642", "fix": "643", "desc": "585"}, {"messageId": "565", "data": "644", "fix": "645", "desc": "588"}, {"messageId": "565", "data": "646", "fix": "647", "desc": "591"}, {"messageId": "565", "data": "648", "fix": "649", "desc": "582"}, {"messageId": "565", "data": "650", "fix": "651", "desc": "585"}, {"messageId": "565", "data": "652", "fix": "653", "desc": "588"}, {"messageId": "565", "data": "654", "fix": "655", "desc": "591"}, [3318, 3397], "const endPage = Math.min(pagination.totalPages, startPage + maxVisiblePages - 1);", {"messageId": "565", "data": "656", "fix": "657", "desc": "582"}, {"messageId": "565", "data": "658", "fix": "659", "desc": "585"}, {"messageId": "565", "data": "660", "fix": "661", "desc": "588"}, {"messageId": "565", "data": "662", "fix": "663", "desc": "591"}, {"messageId": "565", "data": "664", "fix": "665", "desc": "582"}, {"messageId": "565", "data": "666", "fix": "667", "desc": "585"}, {"messageId": "565", "data": "668", "fix": "669", "desc": "588"}, {"messageId": "565", "data": "670", "fix": "671", "desc": "591"}, [2909, 2988], {"messageId": "565", "data": "672", "fix": "673", "desc": "568"}, {"messageId": "565", "data": "674", "fix": "675", "desc": "571"}, {"messageId": "565", "data": "676", "fix": "677", "desc": "574"}, {"messageId": "565", "data": "678", "fix": "679", "desc": "577"}, {"messageId": "565", "data": "680", "fix": "681", "desc": "568"}, {"messageId": "565", "data": "682", "fix": "683", "desc": "571"}, {"messageId": "565", "data": "684", "fix": "685", "desc": "574"}, {"messageId": "565", "data": "686", "fix": "687", "desc": "577"}, {"desc": "688", "fix": "689"}, {"messageId": "549", "fix": "690", "desc": "551"}, {"messageId": "552", "fix": "691", "desc": "554"}, {"messageId": "549", "fix": "692", "desc": "551"}, {"messageId": "552", "fix": "693", "desc": "554"}, {"messageId": "549", "fix": "694", "desc": "551"}, {"messageId": "552", "fix": "695", "desc": "554"}, {"messageId": "549", "fix": "696", "desc": "551"}, {"messageId": "552", "fix": "697", "desc": "554"}, {"messageId": "549", "fix": "698", "desc": "551"}, {"messageId": "552", "fix": "699", "desc": "554"}, {"messageId": "549", "fix": "700", "desc": "551"}, {"messageId": "552", "fix": "701", "desc": "554"}, {"messageId": "549", "fix": "702", "desc": "551"}, {"messageId": "552", "fix": "703", "desc": "554"}, {"messageId": "549", "fix": "704", "desc": "551"}, {"messageId": "552", "fix": "705", "desc": "554"}, {"messageId": "549", "fix": "706", "desc": "551"}, {"messageId": "552", "fix": "707", "desc": "554"}, {"messageId": "549", "fix": "708", "desc": "551"}, {"messageId": "552", "fix": "709", "desc": "554"}, {"messageId": "549", "fix": "710", "desc": "551"}, {"messageId": "552", "fix": "711", "desc": "554"}, {"messageId": "549", "fix": "712", "desc": "551"}, {"messageId": "552", "fix": "713", "desc": "554"}, {"messageId": "549", "fix": "714", "desc": "551"}, {"messageId": "552", "fix": "715", "desc": "554"}, {"messageId": "549", "fix": "716", "desc": "551"}, {"messageId": "552", "fix": "717", "desc": "554"}, {"messageId": "549", "fix": "718", "desc": "551"}, {"messageId": "552", "fix": "719", "desc": "554"}, {"messageId": "549", "fix": "720", "desc": "551"}, {"messageId": "552", "fix": "721", "desc": "554"}, {"messageId": "565", "data": "722", "fix": "723", "desc": "582"}, {"messageId": "565", "data": "724", "fix": "725", "desc": "585"}, {"messageId": "565", "data": "726", "fix": "727", "desc": "588"}, {"messageId": "565", "data": "728", "fix": "729", "desc": "591"}, {"messageId": "565", "data": "730", "fix": "731", "desc": "582"}, {"messageId": "565", "data": "732", "fix": "733", "desc": "585"}, {"messageId": "565", "data": "734", "fix": "735", "desc": "588"}, {"messageId": "565", "data": "736", "fix": "737", "desc": "591"}, "suggestUnknown", {"range": "738", "text": "739"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "740", "text": "741"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "742", "text": "739"}, {"range": "743", "text": "741"}, {"range": "744", "text": "739"}, {"range": "745", "text": "741"}, {"range": "746", "text": "739"}, {"range": "747", "text": "741"}, {"range": "748", "text": "739"}, {"range": "749", "text": "741"}, {"range": "750", "text": "739"}, {"range": "751", "text": "741"}, "replaceWithAlt", {"alt": "752"}, {"range": "753", "text": "754"}, "Replace with `&apos;`.", {"alt": "755"}, {"range": "756", "text": "757"}, "Replace with `&lsquo;`.", {"alt": "758"}, {"range": "759", "text": "760"}, "Replace with `&#39;`.", {"alt": "761"}, {"range": "762", "text": "763"}, "Replace with `&rsquo;`.", {"range": "764", "text": "739"}, {"range": "765", "text": "741"}, {"alt": "766"}, {"range": "767", "text": "768"}, "Replace with `&quot;`.", {"alt": "769"}, {"range": "770", "text": "771"}, "Replace with `&ldquo;`.", {"alt": "772"}, {"range": "773", "text": "774"}, "Replace with `&#34;`.", {"alt": "775"}, {"range": "776", "text": "777"}, "Replace with `&rdquo;`.", {"alt": "766"}, {"range": "778", "text": "779"}, {"alt": "769"}, {"range": "780", "text": "781"}, {"alt": "772"}, {"range": "782", "text": "783"}, {"alt": "775"}, {"range": "784", "text": "785"}, {"alt": "766"}, {"range": "786", "text": "787"}, {"alt": "769"}, {"range": "788", "text": "789"}, {"alt": "772"}, {"range": "790", "text": "791"}, {"alt": "775"}, {"range": "792", "text": "793"}, {"alt": "766"}, {"range": "794", "text": "795"}, {"alt": "769"}, {"range": "796", "text": "797"}, {"alt": "772"}, {"range": "798", "text": "799"}, {"alt": "775"}, {"range": "800", "text": "801"}, {"alt": "752"}, {"range": "802", "text": "803"}, {"alt": "755"}, {"range": "804", "text": "805"}, {"alt": "758"}, {"range": "806", "text": "807"}, {"alt": "761"}, {"range": "808", "text": "809"}, {"alt": "766"}, {"range": "810", "text": "811"}, {"alt": "769"}, {"range": "812", "text": "813"}, {"alt": "772"}, {"range": "814", "text": "815"}, {"alt": "775"}, {"range": "816", "text": "817"}, {"alt": "766"}, {"range": "818", "text": "819"}, {"alt": "769"}, {"range": "820", "text": "821"}, {"alt": "772"}, {"range": "822", "text": "823"}, {"alt": "775"}, {"range": "824", "text": "825"}, {"alt": "766"}, {"range": "826", "text": "827"}, {"alt": "769"}, {"range": "828", "text": "829"}, {"alt": "772"}, {"range": "830", "text": "831"}, {"alt": "775"}, {"range": "832", "text": "833"}, {"alt": "766"}, {"range": "834", "text": "835"}, {"alt": "769"}, {"range": "836", "text": "837"}, {"alt": "772"}, {"range": "838", "text": "839"}, {"alt": "775"}, {"range": "840", "text": "841"}, {"alt": "766"}, {"range": "842", "text": "843"}, {"alt": "769"}, {"range": "844", "text": "845"}, {"alt": "772"}, {"range": "846", "text": "847"}, {"alt": "775"}, {"range": "848", "text": "849"}, {"alt": "766"}, {"range": "850", "text": "766"}, {"alt": "769"}, {"range": "851", "text": "769"}, {"alt": "772"}, {"range": "852", "text": "772"}, {"alt": "775"}, {"range": "853", "text": "775"}, {"alt": "752"}, {"range": "854", "text": "855"}, {"alt": "755"}, {"range": "856", "text": "857"}, {"alt": "758"}, {"range": "858", "text": "859"}, {"alt": "761"}, {"range": "860", "text": "861"}, {"alt": "752"}, {"range": "862", "text": "863"}, {"alt": "755"}, {"range": "864", "text": "865"}, {"alt": "758"}, {"range": "866", "text": "867"}, {"alt": "761"}, {"range": "868", "text": "869"}, "Update the dependencies array to be: [checkExistingSession]", {"range": "870", "text": "871"}, {"range": "872", "text": "739"}, {"range": "873", "text": "741"}, {"range": "874", "text": "739"}, {"range": "875", "text": "741"}, {"range": "876", "text": "739"}, {"range": "877", "text": "741"}, {"range": "878", "text": "739"}, {"range": "879", "text": "741"}, {"range": "880", "text": "739"}, {"range": "881", "text": "741"}, {"range": "882", "text": "739"}, {"range": "883", "text": "741"}, {"range": "884", "text": "739"}, {"range": "885", "text": "741"}, {"range": "886", "text": "739"}, {"range": "887", "text": "741"}, {"range": "888", "text": "739"}, {"range": "889", "text": "741"}, {"range": "890", "text": "739"}, {"range": "891", "text": "741"}, {"range": "892", "text": "739"}, {"range": "893", "text": "741"}, {"range": "894", "text": "739"}, {"range": "895", "text": "741"}, {"range": "896", "text": "739"}, {"range": "897", "text": "741"}, {"range": "898", "text": "739"}, {"range": "899", "text": "741"}, {"range": "900", "text": "739"}, {"range": "901", "text": "741"}, {"range": "902", "text": "739"}, {"range": "903", "text": "741"}, {"alt": "766"}, {"range": "904", "text": "905"}, {"alt": "769"}, {"range": "906", "text": "907"}, {"alt": "772"}, {"range": "908", "text": "909"}, {"alt": "775"}, {"range": "910", "text": "911"}, {"alt": "766"}, {"range": "912", "text": "913"}, {"alt": "769"}, {"range": "914", "text": "915"}, {"alt": "772"}, {"range": "916", "text": "917"}, {"alt": "775"}, {"range": "918", "text": "919"}, [934, 937], "unknown", [934, 937], "never", [945, 948], [945, 948], [3262, 3265], [3262, 3265], [3273, 3276], [3273, 3276], [406, 409], [406, 409], [6287, 6290], [6287, 6290], "&apos;", [10671, 10708], "\n              Don&apos;t have an account?", "&lsquo;", [10671, 10708], "\n              Don&lsquo;t have an account?", "&#39;", [10671, 10708], "\n              Don&#39;t have an account?", "&rsquo;", [10671, 10708], "\n              Don&rsquo;t have an account?", [11121, 11124], [11121, 11124], "&quot;", [6815, 6854], "\n                  Search Results for &quot;", "&ldquo;", [6815, 6854], "\n                  Search Results for &ldquo;", "&#34;", [6815, 6854], "\n                  Search Results for &#34;", "&rdquo;", [6815, 6854], "\n                  Search Results for &rdquo;", [6864, 6882], "&quot;\n                ", [6864, 6882], "&ldquo;\n                ", [6864, 6882], "&#34;\n                ", [6864, 6882], "&rdquo;\n                ", [8097, 8150], "\n                No companies match your search for &quot;", [8097, 8150], "\n                No companies match your search for &ldquo;", [8097, 8150], "\n                No companies match your search for &#34;", [8097, 8150], "\n                No companies match your search for &rdquo;", [8160, 8225], "&quot;. Try different keywords or browse all companies.\n              ", [8160, 8225], "&ldquo;. Try different keywords or browse all companies.\n              ", [8160, 8225], "&#34;. Try different keywords or browse all companies.\n              ", [8160, 8225], "&rdquo;. Try different keywords or browse all companies.\n              ", [1850, 1896], "You don&apos;t have permission to access this area.", [1850, 1896], "You don&lsquo;t have permission to access this area.", [1850, 1896], "You don&#39;t have permission to access this area.", [1850, 1896], "You don&rsquo;t have permission to access this area.", [5793, 5832], "\n              No companies found for &quot;", [5793, 5832], "\n              No companies found for &ldquo;", [5793, 5832], "\n              No companies found for &#34;", [5793, 5832], "\n              No companies found for &rdquo;", [5839, 5853], "&quot;\n            ", [5839, 5853], "&ldquo;\n            ", [5839, 5853], "&#34;\n            ", [5839, 5853], "&rdquo;\n            ", [6924, 6963], "\n                View all results for &quot;", [6924, 6963], "\n                View all results for &ldquo;", [6924, 6963], "\n                View all results for &#34;", [6924, 6963], "\n                View all results for &rdquo;", [6970, 6986], "&quot;\n              ", [6970, 6986], "&ldquo;\n              ", [6970, 6986], "&#34;\n              ", [6970, 6986], "&rdquo;\n              ", [8400, 8406], " for &quot;", [8400, 8406], " for &ldquo;", [8400, 8406], " for &#34;", [8400, 8406], " for &rdquo;", [8428, 8429], [8428, 8429], [8428, 8429], [8428, 8429], [26830, 27054], "\n                  By submitting this review, you confirm that it&apos;s based on your genuine experience\n                  and doesn't contain false information. Your review will be moderated before publication.\n                ", [26830, 27054], "\n                  By submitting this review, you confirm that it&lsquo;s based on your genuine experience\n                  and doesn't contain false information. Your review will be moderated before publication.\n                ", [26830, 27054], "\n                  By submitting this review, you confirm that it&#39;s based on your genuine experience\n                  and doesn't contain false information. Your review will be moderated before publication.\n                ", [26830, 27054], "\n                  By submitting this review, you confirm that it&rsquo;s based on your genuine experience\n                  and doesn't contain false information. Your review will be moderated before publication.\n                ", [26830, 27054], "\n                  By submitting this review, you confirm that it's based on your genuine experience\n                  and doesn&apos;t contain false information. Your review will be moderated before publication.\n                ", [26830, 27054], "\n                  By submitting this review, you confirm that it's based on your genuine experience\n                  and doesn&lsquo;t contain false information. Your review will be moderated before publication.\n                ", [26830, 27054], "\n                  By submitting this review, you confirm that it's based on your genuine experience\n                  and doesn&#39;t contain false information. Your review will be moderated before publication.\n                ", [26830, 27054], "\n                  By submitting this review, you confirm that it's based on your genuine experience\n                  and doesn&rsquo;t contain false information. Your review will be moderated before publication.\n                ", [2386, 2388], "[checkExistingSession]", [322, 325], [322, 325], [1881, 1884], [1881, 1884], [1892, 1895], [1892, 1895], [1973, 1976], [1973, 1976], [4053, 4056], [4053, 4056], [528, 531], [528, 531], [3600, 3603], [3600, 3603], [3933, 3936], [3933, 3936], [4083, 4086], [4083, 4086], [3242, 3245], [3242, 3245], [826, 829], [826, 829], [839, 842], [839, 842], [1143, 1146], [1143, 1146], [3680, 3683], [3680, 3683], [199, 202], [199, 202], [231, 234], [231, 234], [1681, 1710], "Type &quot;tech\" in the search bar", [1681, 1710], "Type &ldquo;tech\" in the search bar", [1681, 1710], "Type &#34;tech\" in the search bar", [1681, 1710], "Type &rdquo;tech\" in the search bar", [1681, 1710], "Type \"tech&quot; in the search bar", [1681, 1710], "Type \"tech&ldquo; in the search bar", [1681, 1710], "Type \"tech&#34; in the search bar", [1681, 1710], "Type \"tech&rdquo; in the search bar"]