import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { ValidationUtils } from '@/lib/sanitization';

/**
 * GET /api/companies/[id]
 * Public endpoint to get single company details
 * Following HAQ-rules.md PUB-03 specification
 * 
 * Path Parameters:
 * - id: Company UUID
 * 
 * Response includes:
 * - Company details (name, industry, location, etc.)
 * - Aggregated review statistics
 * - NO individual review data (use /api/companies/[id]/reviews for that)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const companyId = params.id;

    // Validate UUID format
    if (!ValidationUtils.isValidUUID(companyId)) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Invalid company ID format',
          error: 'Company ID must be a valid UUID'
        },
        { status: 400 }
      );
    }

    // Fetch company details from content schema (RULE-102)
    const { data: company, error: companyError } = await supabase
      .from('haq_content_db.companies')
      .select(`
        company_id,
        name,
        slug,
        industry,
        location,
        website_url,
        logo_url,
        employee_count_range,
        founded_year,
        description,
        haq_score,
        total_reviews,
        is_verified,
        created_at,
        updated_at
      `)
      .eq('company_id', companyId)
      .single();

    if (companyError || !company) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Company not found',
          error: 'The requested company does not exist'
        },
        { status: 404 }
      );
    }

    // Get aggregated review statistics for approved reviews only from content schema (RULE-102)
    const { data: reviewStats, error: statsError } = await supabase
      .from('haq_content_db.reviews')
      .select('overall_rating')
      .eq('company_id', companyId)
      .eq('is_approved', true); // Only approved reviews

    // Calculate review statistics
    let reviewStatistics = {
      total_reviews: 0,
      average_rating: 0,
      rating_distribution: {
        1: 0,
        2: 0,
        3: 0,
        4: 0,
        5: 0
      }
    };

    if (reviewStats && reviewStats.length > 0) {
      reviewStatistics.total_reviews = reviewStats.length;
      
      // Calculate average rating
      const totalRating = reviewStats.reduce((sum, review) => sum + review.overall_rating, 0);
      reviewStatistics.average_rating = Math.round((totalRating / reviewStats.length) * 10) / 10;
      
      // Calculate rating distribution
      reviewStats.forEach(review => {
        reviewStatistics.rating_distribution[review.overall_rating as keyof typeof reviewStatistics.rating_distribution]++;
      });
    }

    // Prepare response data
    const responseData = {
      company: {
        ...company,
        // Override with calculated statistics
        total_reviews: reviewStatistics.total_reviews,
        average_rating: reviewStatistics.average_rating,
        rating_distribution: reviewStatistics.rating_distribution
      }
    };

    return NextResponse.json({
      success: true,
      data: responseData
    }, {
      headers: {
        // CDN_SHORT caching policy as per HAQ-rules.md
        'Cache-Control': 'public, max-age=300, s-maxage=300', // 5 minutes
        'Vary': 'Accept-Encoding',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY'
      }
    });

  } catch (error) {
    console.error('Company details API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error',
        error: 'Unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * Handle unsupported HTTP methods
 */
export async function POST() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}
