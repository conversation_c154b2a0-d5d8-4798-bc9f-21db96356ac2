{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useReducer, useEffect, useCallback, useMemo } from 'react';\nimport { User } from '@/lib/auth-client';\n\n// Types\ninterface AuthState {\n  user: User | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  error: string | null;\n}\n\ninterface AuthContextType extends AuthState {\n  login: (email: string, password: string) => Promise<{ success: boolean; message: string }>;\n  register: (username: string, email: string, password: string) => Promise<{ success: boolean; message: string }>;\n  logout: () => Promise<void>;\n  clearError: () => void;\n  refreshUser: () => Promise<void>;\n}\n\n// Action types\ntype AuthAction =\n  | { type: 'AUTH_START' }\n  | { type: 'AUTH_SUCCESS'; payload: User }\n  | { type: 'AUTH_FAILURE'; payload: string }\n  | { type: 'AUTH_LOGOUT' }\n  | { type: 'CLEAR_ERROR' }\n  | { type: 'SET_LOADING'; payload: boolean };\n\n// Initial state\nconst initialState: AuthState = {\n  user: null,\n  isLoading: true, // Start with loading true to check existing session\n  isAuthenticated: false,\n  error: null,\n};\n\n// Reducer\nfunction authReducer(state: AuthState, action: AuthAction): AuthState {\n  switch (action.type) {\n    case 'AUTH_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n    case 'AUTH_SUCCESS':\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n    case 'AUTH_FAILURE':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n    case 'AUTH_LOGOUT':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      };\n    case 'CLEAR_ERROR':\n      return {\n        ...state,\n        error: null,\n      };\n    case 'SET_LOADING':\n      return {\n        ...state,\n        isLoading: action.payload,\n      };\n    default:\n      return state;\n  }\n}\n\n// Create contexts\nconst AuthContext = createContext<AuthContextType | null>(null);\n\n// Provider component\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Check for existing session on mount\n  useEffect(() => {\n    checkExistingSession();\n  }, []);\n\n  // Check if user is already logged in\n  const checkExistingSession = useCallback(async () => {\n    try {\n      dispatch({ type: 'SET_LOADING', payload: true });\n\n      const response = await fetch('/api/auth/me', {\n        method: 'GET',\n        credentials: 'include', // Include cookies\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        if (result.success && result.user) {\n          // ANONYMITY PROTECTION: No user identification in logs per RULE-601\n          dispatch({ type: 'AUTH_SUCCESS', payload: result.user });\n        } else {\n          dispatch({ type: 'AUTH_LOGOUT' });\n        }\n      } else {\n        dispatch({ type: 'AUTH_LOGOUT' });\n      }\n    } catch (error) {\n      // ANONYMITY PROTECTION: No detailed error logging per RULE-601\n      dispatch({ type: 'AUTH_LOGOUT' });\n    } finally {\n      dispatch({ type: 'SET_LOADING', payload: false });\n    }\n  }, []);\n\n  // Login function\n  const login = useCallback(async (email: string, password: string) => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include', // Include cookies\n        body: JSON.stringify({ email, password }),\n      });\n\n      const result = await response.json();\n\n      if (result.success && result.user) {\n        dispatch({ type: 'AUTH_SUCCESS', payload: result.user });\n        return { success: true, message: result.message };\n      } else {\n        dispatch({ type: 'AUTH_FAILURE', payload: result.message });\n        return { success: false, message: result.message };\n      }\n    } catch (error) {\n      const errorMessage = 'Network error. Please try again.';\n      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });\n      return { success: false, message: errorMessage };\n    }\n  }, []);\n\n  // Register function\n  const register = useCallback(async (username: string, email: string, password: string) => {\n    try {\n      dispatch({ type: 'AUTH_START' });\n\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include', // Include cookies\n        body: JSON.stringify({ username, email, password }),\n      });\n\n      const result = await response.json();\n\n      if (result.success && result.user) {\n        dispatch({ type: 'AUTH_SUCCESS', payload: result.user });\n        return { success: true, message: result.message };\n      } else {\n        dispatch({ type: 'AUTH_FAILURE', payload: result.message });\n        return { success: false, message: result.message };\n      }\n    } catch (error) {\n      const errorMessage = 'Network error. Please try again.';\n      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });\n      return { success: false, message: errorMessage };\n    }\n  }, []);\n\n  // Logout function\n  const logout = useCallback(async () => {\n    try {\n      dispatch({ type: 'SET_LOADING', payload: true });\n\n      // Call logout API to clear server-side session\n      await fetch('/api/auth/logout', {\n        method: 'POST',\n        credentials: 'include', // Include cookies\n      });\n\n      // Clear client-side state regardless of API response\n      dispatch({ type: 'AUTH_LOGOUT' });\n    } catch (error) {\n      // ANONYMITY PROTECTION: No detailed error logging per RULE-601\n      // Still clear client-side state even if API call fails\n      dispatch({ type: 'AUTH_LOGOUT' });\n    } finally {\n      dispatch({ type: 'SET_LOADING', payload: false });\n    }\n  }, []);\n\n  // Clear error function\n  const clearError = useCallback(() => {\n    dispatch({ type: 'CLEAR_ERROR' });\n  }, []);\n\n  // Refresh user data\n  const refreshUser = useCallback(async () => {\n    await checkExistingSession();\n  }, [checkExistingSession]);\n\n  // Memoize context value to prevent unnecessary re-renders\n  const contextValue = useMemo(\n    () => ({\n      ...state,\n      login,\n      register,\n      logout,\n      clearError,\n      refreshUser,\n    }),\n    [state, login, register, logout, clearError, refreshUser]\n  );\n\n  return (\n    <AuthContext.Provider value={contextValue}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\n// Custom hook to use auth context\nexport function useAuth(): AuthContextType {\n  const context = useContext(AuthContext);\n  \n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  \n  return context;\n}\n\n// Higher-order component for protected routes\nexport function withAuth<P extends object>(\n  Component: React.ComponentType<P>\n): React.ComponentType<P> {\n  return function AuthenticatedComponent(props: P) {\n    const { isAuthenticated, isLoading } = useAuth();\n\n    if (isLoading) {\n      return (\n        <div className=\"min-h-screen bg-background-primary flex items-center justify-center\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-accent-primary mx-auto mb-4\"></div>\n            <p className=\"text-text-secondary\">Loading...</p>\n          </div>\n        </div>\n      );\n    }\n\n    if (!isAuthenticated) {\n      // Redirect to login page\n      if (typeof window !== 'undefined') {\n        window.location.href = '/auth/login';\n      }\n      return null;\n    }\n\n    return <Component {...props} />;\n  };\n}\n\n// Hook for checking if user has specific role\nexport function useRole(requiredRole: 'user' | 'admin'): boolean {\n  const { user, isAuthenticated } = useAuth();\n  \n  if (!isAuthenticated || !user) {\n    return false;\n  }\n  \n  if (requiredRole === 'admin') {\n    return user.role === 'admin';\n  }\n  \n  // For 'user' role, both 'user' and 'admin' are allowed\n  return user.role === 'user' || user.role === 'admin';\n}\n\n// Hook for admin-only access\nexport function useAdminOnly(): boolean {\n  return useRole('admin');\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;;;AAFA;;AA8BA,gBAAgB;AAChB,MAAM,eAA0B;IAC9B,MAAM;IACN,WAAW;IACX,iBAAiB;IACjB,OAAO;AACT;AAEA,UAAU;AACV,SAAS,YAAY,KAAgB,EAAE,MAAkB;IACvD,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,WAAW;gBACX,OAAO;YACT;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM,OAAO,OAAO;gBACpB,iBAAiB;gBACjB,WAAW;gBACX,OAAO;YACT;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM;gBACN,iBAAiB;gBACjB,WAAW;gBACX,OAAO,OAAO,OAAO;YACvB;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM;gBACN,iBAAiB;gBACjB,WAAW;gBACX,OAAO;YACT;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,OAAO;YACT;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,WAAW,OAAO,OAAO;YAC3B;QACF;YACE,OAAO;IACX;AACF;AAEA,kBAAkB;AAClB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA0B;AAGnD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAElD,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,qCAAqC;IACrC,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACvC,IAAI;gBACF,SAAS;oBAAE,MAAM;oBAAe,SAAS;gBAAK;gBAE9C,MAAM,WAAW,MAAM,MAAM,gBAAgB;oBAC3C,QAAQ;oBACR,aAAa;gBACf;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,SAAS,MAAM,SAAS,IAAI;oBAClC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;wBACjC,oEAAoE;wBACpE,SAAS;4BAAE,MAAM;4BAAgB,SAAS,OAAO,IAAI;wBAAC;oBACxD,OAAO;wBACL,SAAS;4BAAE,MAAM;wBAAc;oBACjC;gBACF,OAAO;oBACL,SAAS;wBAAE,MAAM;oBAAc;gBACjC;YACF,EAAE,OAAO,OAAO;gBACd,+DAA+D;gBAC/D,SAAS;oBAAE,MAAM;gBAAc;YACjC,SAAU;gBACR,SAAS;oBAAE,MAAM;oBAAe,SAAS;gBAAM;YACjD;QACF;yDAAG,EAAE;IAEL,iBAAiB;IACjB,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE,OAAO,OAAe;YAC9C,IAAI;gBACF,SAAS;oBAAE,MAAM;gBAAa;gBAE9B,MAAM,WAAW,MAAM,MAAM,mBAAmB;oBAC9C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,aAAa;oBACb,MAAM,KAAK,SAAS,CAAC;wBAAE;wBAAO;oBAAS;gBACzC;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;oBACjC,SAAS;wBAAE,MAAM;wBAAgB,SAAS,OAAO,IAAI;oBAAC;oBACtD,OAAO;wBAAE,SAAS;wBAAM,SAAS,OAAO,OAAO;oBAAC;gBAClD,OAAO;oBACL,SAAS;wBAAE,MAAM;wBAAgB,SAAS,OAAO,OAAO;oBAAC;oBACzD,OAAO;wBAAE,SAAS;wBAAO,SAAS,OAAO,OAAO;oBAAC;gBACnD;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe;gBACrB,SAAS;oBAAE,MAAM;oBAAgB,SAAS;gBAAa;gBACvD,OAAO;oBAAE,SAAS;oBAAO,SAAS;gBAAa;YACjD;QACF;0CAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,OAAO,UAAkB,OAAe;YACnE,IAAI;gBACF,SAAS;oBAAE,MAAM;gBAAa;gBAE9B,MAAM,WAAW,MAAM,MAAM,sBAAsB;oBACjD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,aAAa;oBACb,MAAM,KAAK,SAAS,CAAC;wBAAE;wBAAU;wBAAO;oBAAS;gBACnD;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;oBACjC,SAAS;wBAAE,MAAM;wBAAgB,SAAS,OAAO,IAAI;oBAAC;oBACtD,OAAO;wBAAE,SAAS;wBAAM,SAAS,OAAO,OAAO;oBAAC;gBAClD,OAAO;oBACL,SAAS;wBAAE,MAAM;wBAAgB,SAAS,OAAO,OAAO;oBAAC;oBACzD,OAAO;wBAAE,SAAS;wBAAO,SAAS,OAAO,OAAO;oBAAC;gBACnD;YACF,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe;gBACrB,SAAS;oBAAE,MAAM;oBAAgB,SAAS;gBAAa;gBACvD,OAAO;oBAAE,SAAS;oBAAO,SAAS;gBAAa;YACjD;QACF;6CAAG,EAAE;IAEL,kBAAkB;IAClB,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE;YACzB,IAAI;gBACF,SAAS;oBAAE,MAAM;oBAAe,SAAS;gBAAK;gBAE9C,+CAA+C;gBAC/C,MAAM,MAAM,oBAAoB;oBAC9B,QAAQ;oBACR,aAAa;gBACf;gBAEA,qDAAqD;gBACrD,SAAS;oBAAE,MAAM;gBAAc;YACjC,EAAE,OAAO,OAAO;gBACd,+DAA+D;gBAC/D,uDAAuD;gBACvD,SAAS;oBAAE,MAAM;gBAAc;YACjC,SAAU;gBACR,SAAS;oBAAE,MAAM;oBAAe,SAAS;gBAAM;YACjD;QACF;2CAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAC7B,SAAS;gBAAE,MAAM;YAAc;QACjC;+CAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAC9B,MAAM;QACR;gDAAG;QAAC;KAAqB;IAEzB,0DAA0D;IAC1D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CACzB,IAAM,CAAC;gBACL,GAAG,KAAK;gBACR;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;6CACD;QAAC;QAAO;QAAO;QAAU;QAAQ;QAAY;KAAY;IAG3D,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GAnJgB;KAAA;AAsJT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;IARgB;AAWT,SAAS,SACd,SAAiC;;IAEjC,UAAO,SAAS,uBAAuB,KAAQ;;QAC7C,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG;QAEvC,IAAI,WAAW;YACb,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAsB;;;;;;;;;;;;;;;;;QAI3C;QAEA,IAAI,CAAC,iBAAiB;YACpB,yBAAyB;YACzB,wCAAmC;gBACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;YACA,OAAO;QACT;QAEA,qBAAO,6LAAC;YAAW,GAAG,KAAK;;;;;;IAC7B;;YAtByC;;;AAuB3C;AAGO,SAAS,QAAQ,YAA8B;;IACpD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG;IAElC,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAC7B,OAAO;IACT;IAEA,IAAI,iBAAiB,SAAS;QAC5B,OAAO,KAAK,IAAI,KAAK;IACvB;IAEA,uDAAuD;IACvD,OAAO,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,KAAK;AAC/C;IAbgB;;QACoB;;;AAe7B,SAAS;;IACd,OAAO,QAAQ;AACjB;IAFgB;;QACP", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Search, Menu, X, Shield, Users, DollarSign, MessageCircle, LogIn, LogOut, User, Settings } from 'lucide-react';\nimport { useAuth } from '@/contexts/AuthContext';\n\nexport const Header: React.FC = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\n  const pathname = usePathname();\n  const { user, isAuthenticated, isLoading, logout } = useAuth();\n\n  const navigation = [\n    { name: 'Companies', href: '/companies', icon: Shield },\n    { name: 'Salaries', href: '/salaries', icon: DollarSign },\n    { name: 'Community', href: '/community', icon: MessageCircle },\n  ];\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n      setIsUserMenuOpen(false);\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n\n  return (\n    <header className=\"bg-background-primary border-b border-border-primary sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <div className=\"w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-200\">\n              <Shield className=\"w-5 h-5 text-text-on-accent\" />\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-xl font-bold text-accent-primary\">Haq</span>\n              <span className=\"text-xs text-text-secondary -mt-1\">حق</span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-navigation-link font-medium transition-all duration-200 uppercase tracking-wide ${\n                    isActive\n                      ? 'text-accent-primary bg-surface-secondary'\n                      : 'text-text-secondary hover:text-accent-primary hover:bg-surface-primary'\n                  }`}\n                >\n                  <Icon className=\"w-4 h-4\" />\n                  <span>{item.name}</span>\n                </Link>\n              );\n            })}\n          </nav>\n\n          {/* Action Buttons */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {isAuthenticated && (\n              <Link\n                href=\"/review/submit\"\n                className=\"bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 flex items-center space-x-2 hover:shadow-glow transform hover:-translate-y-0.5\"\n              >\n                <Users className=\"w-4 h-4\" />\n                <span>Write Review</span>\n              </Link>\n            )}\n\n            {/* Authentication Section */}\n            {isLoading ? (\n              <div className=\"w-8 h-8 animate-spin rounded-full border-2 border-accent-primary border-t-transparent\"></div>\n            ) : isAuthenticated && user ? (\n              /* User Menu */\n              <div className=\"relative\">\n                <button\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\n                  className=\"flex items-center space-x-2 px-3 py-2 rounded-lg text-text-secondary hover:text-accent-primary hover:bg-surface-primary transition-all duration-200\"\n                >\n                  <div className=\"w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-full flex items-center justify-center\">\n                    <User className=\"w-4 h-4 text-text-on-accent\" />\n                  </div>\n                  <span className=\"text-sm font-medium\">{user.username}</span>\n                  <svg className={`w-4 h-4 transition-transform duration-200 ${isUserMenuOpen ? 'rotate-180' : ''}`} fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n\n                {/* User Dropdown Menu */}\n                {isUserMenuOpen && (\n                  <div className=\"absolute right-0 mt-2 w-48 bg-surface-primary border border-border-primary rounded-lg shadow-lg py-1 z-50\">\n                    <div className=\"px-4 py-2 border-b border-border-primary\">\n                      <p className=\"text-sm font-medium text-text-primary\">{user.username}</p>\n                      <p className=\"text-xs text-text-secondary\">{user.email}</p>\n                      <span className={`inline-block px-2 py-1 text-xs rounded-full mt-1 ${\n                        user.role === 'admin'\n                          ? 'bg-accent-primary text-text-on-accent'\n                          : 'bg-surface-secondary text-text-secondary'\n                      }`}>\n                        {user.role}\n                      </span>\n                    </div>\n                    <Link\n                      href=\"/profile\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                      className=\"flex items-center space-x-2 px-4 py-2 text-sm text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200\"\n                    >\n                      <Settings className=\"w-4 h-4\" />\n                      <span>Profile Settings</span>\n                    </Link>\n                    <button\n                      onClick={handleLogout}\n                      className=\"w-full flex items-center space-x-2 px-4 py-2 text-sm text-text-secondary hover:text-red-600 hover:bg-surface-secondary transition-colors duration-200\"\n                    >\n                      <LogOut className=\"w-4 h-4\" />\n                      <span>Sign Out</span>\n                    </button>\n                  </div>\n                )}\n              </div>\n            ) : (\n              /* Login/Register Buttons */\n              <div className=\"flex items-center space-x-3\">\n                <Link\n                  href=\"/auth/login\"\n                  className=\"flex items-center space-x-1 px-4 py-2 text-sm font-medium text-text-secondary hover:text-accent-primary transition-colors duration-200\"\n                >\n                  <LogIn className=\"w-4 h-4\" />\n                  <span>Sign In</span>\n                </Link>\n                <Link\n                  href=\"/auth/signup\"\n                  className=\"bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-4 py-2 rounded-lg text-sm font-semibold transition-all duration-200 flex items-center space-x-1 hover:shadow-glow transform hover:-translate-y-0.5\"\n                >\n                  <User className=\"w-4 h-4\" />\n                  <span>Sign Up</span>\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className=\"md:hidden p-2 rounded-lg text-text-secondary hover:text-accent-primary hover:bg-surface-primary transition-colors duration-200\"\n          >\n            {isMenuOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isMenuOpen && (\n        <div className=\"md:hidden bg-surface-primary border-t border-border-primary\">\n          <div className=\"px-4 py-3 space-y-3\">\n            {navigation.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n              return (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMenuOpen(false)}\n                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${\n                    isActive\n                      ? 'text-accent-primary bg-surface-secondary'\n                      : 'text-text-secondary hover:text-accent-primary hover:bg-surface-secondary'\n                  }`}\n                >\n                  <Icon className=\"w-5 h-5\" />\n                  <span>{item.name}</span>\n                </Link>\n              );\n            })}\n\n            {/* Mobile Authentication Section */}\n            {isAuthenticated ? (\n              <>\n                <Link\n                  href=\"/review/submit\"\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"flex items-center space-x-3 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200\"\n                >\n                  <Users className=\"w-5 h-5\" />\n                  <span>Write Review</span>\n                </Link>\n\n                <div className=\"border-t border-border-primary pt-3 mt-3\">\n                  <div className=\"flex items-center space-x-3 px-3 py-2 mb-2\">\n                    <div className=\"w-8 h-8 bg-gradient-to-br from-accent-primary to-accent-secondary rounded-full flex items-center justify-center\">\n                      <User className=\"w-4 h-4 text-text-on-accent\" />\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-text-primary\">{user?.username}</p>\n                      <p className=\"text-xs text-text-secondary\">{user?.email}</p>\n                    </div>\n                  </div>\n\n                  <Link\n                    href=\"/profile\"\n                    onClick={() => setIsMenuOpen(false)}\n                    className=\"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200\"\n                  >\n                    <Settings className=\"w-5 h-5\" />\n                    <span>Profile Settings</span>\n                  </Link>\n\n                  <button\n                    onClick={() => {\n                      handleLogout();\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-red-600 hover:bg-surface-secondary transition-colors duration-200\"\n                  >\n                    <LogOut className=\"w-5 h-5\" />\n                    <span>Sign Out</span>\n                  </button>\n                </div>\n              </>\n            ) : (\n              <div className=\"border-t border-border-primary pt-3 mt-3 space-y-2\">\n                <Link\n                  href=\"/auth/login\"\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium text-text-secondary hover:text-accent-primary hover:bg-surface-secondary transition-colors duration-200\"\n                >\n                  <LogIn className=\"w-5 h-5\" />\n                  <span>Sign In</span>\n                </Link>\n                <Link\n                  href=\"/auth/signup\"\n                  onClick={() => setIsMenuOpen(false)}\n                  className=\"flex items-center space-x-3 bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200\"\n                >\n                  <User className=\"w-5 h-5\" />\n                  <span>Sign Up</span>\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQO,MAAM,SAAmB;;IAC9B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE3D,MAAM,aAAa;QACjB;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,yMAAA,CAAA,SAAM;QAAC;QACtD;YAAE,MAAM;YAAY,MAAM;YAAa,MAAM,qNAAA,CAAA,aAAU;QAAC;QACxD;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,2NAAA,CAAA,gBAAa;QAAC;KAC9D;IAED,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAwC;;;;;;sDACxD,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAKxD,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,sIAAsI,EAChJ,WACI,6CACA,0EACJ;;sDAEF,6LAAC;4CAAK,WAAU;;;;;;sDAChB,6LAAC;sDAAM,KAAK,IAAI;;;;;;;mCATX,KAAK,IAAI;;;;;4BAYpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;gCACZ,iCACC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;gCAKT,0BACC,6LAAC;oCAAI,WAAU;;;;;2CACb,mBAAmB,OACrB,aAAa,iBACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,kBAAkB,CAAC;4CAClC,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6LAAC;oDAAK,WAAU;8DAAuB,KAAK,QAAQ;;;;;;8DACpD,6LAAC;oDAAI,WAAW,CAAC,0CAA0C,EAAE,iBAAiB,eAAe,IAAI;oDAAE,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC3I,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;wCAKxE,gCACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAyC,KAAK,QAAQ;;;;;;sEACnE,6LAAC;4DAAE,WAAU;sEAA+B,KAAK,KAAK;;;;;;sEACtD,6LAAC;4DAAK,WAAW,CAAC,iDAAiD,EACjE,KAAK,IAAI,KAAK,UACV,0CACA,4CACJ;sEACC,KAAK,IAAI;;;;;;;;;;;;8DAGd,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,SAAS,IAAM,kBAAkB;oDACjC,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;sEAAK;;;;;;;;;;;;8DAER,6LAAC;oDACC,SAAS;oDACT,WAAU;;sEAEV,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;2CAMd,0BAA0B,iBAC1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,2MAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAOd,6LAAC;4BACC,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAU;sCAET,2BAAa,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAM/D,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,OAAO,KAAK,IAAI;4BACtB,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,SAAS,IAAM,cAAc;gCAC7B,WAAW,CAAC,oGAAoG,EAC9G,WACI,6CACA,4EACJ;;kDAEF,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;kDAAM,KAAK,IAAI;;;;;;;+BAVX,KAAK,IAAI;;;;;wBAapB;wBAGC,gCACC;;8CACE,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;;sDAEV,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;8CAGR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAAyC,MAAM;;;;;;sEAC5D,6LAAC;4DAAE,WAAU;sEAA+B,MAAM;;;;;;;;;;;;;;;;;;sDAItD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,SAAS,IAAM,cAAc;4CAC7B,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;8DAAK;;;;;;;;;;;;sDAGR,6LAAC;4CACC,SAAS;gDACP;gDACA,cAAc;4CAChB;4CACA,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;yDAKZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;;sDAEV,6LAAC,2MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GArPa;;QAGM,qIAAA,CAAA,cAAW;QACyB,kIAAA,CAAA,UAAO;;;KAJjD", "debugId": null}}, {"offset": {"line": 1104, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\n// Supabase configuration\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Singleton pattern to prevent multiple client instances (Context7 best practice)\nlet browserClientInstance: ReturnType<typeof createBrowserClient> | null = null\nlet serverClientInstance: ReturnType<typeof createClient> | null = null\n\n// Client-side Supabase client (for browser components)\nexport const createClientComponentClient = () => {\n  if (typeof window === 'undefined') {\n    // Server-side: create a new instance each time\n    return createBrowserClient(supabaseUrl, supabaseAnonKey)\n  }\n\n  // Client-side: use singleton\n  if (!browserClientInstance) {\n    browserClientInstance = createBrowserClient(supabaseUrl, supabase<PERSON>non<PERSON>ey)\n  }\n  return browserClientInstance\n}\n\n// Server-side Supabase client (for API routes and server components)\nexport const createServerClient = () => {\n  if (!serverClientInstance) {\n    serverClientInstance = createClient(supabaseUrl, supabaseAnonKey)\n  }\n  return serverClientInstance\n}\n\n// Main client for general use (uses public schema by default)\nexport const supabase = createServerClient()\n\n// Database types (will be generated later)\nexport type Database = {\n  haq_users_db: {\n    Tables: {\n      users: {\n        Row: {\n          user_id: string\n          username: string\n          email: string\n          password_hash: string\n          role: 'user' | 'admin'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          user_id?: string\n          username: string\n          email: string\n          password_hash: string\n          role?: 'user' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          user_id?: string\n          username?: string\n          email?: string\n          password_hash?: string\n          role?: 'user' | 'admin'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n  }\n  haq_content_db: {\n    Tables: {\n      companies: {\n        Row: {\n          company_id: string\n          name: string\n          slug: string\n          industry: string | null\n          location: string | null\n          description: string | null\n          website_url: string | null\n          logo_url: string | null\n          employee_count_range: string | null\n          founded_year: number | null\n          haq_score: number\n          total_reviews: number\n          is_verified: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          company_id?: string\n          name: string\n          slug: string\n          industry?: string | null\n          location?: string | null\n          description?: string | null\n          website_url?: string | null\n          logo_url?: string | null\n          employee_count_range?: string | null\n          founded_year?: number | null\n          haq_score?: number\n          total_reviews?: number\n          is_verified?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          company_id?: string\n          name?: string\n          slug?: string\n          industry?: string | null\n          location?: string | null\n          description?: string | null\n          website_url?: string | null\n          logo_url?: string | null\n          employee_count_range?: string | null\n          founded_year?: number | null\n          haq_score?: number\n          total_reviews?: number\n          is_verified?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      reviews: {\n        Row: {\n          review_id: string\n          company_id: string\n          anonymous_user_hash: string\n          overall_rating: number | null\n          work_life_balance_rating: number | null\n          compensation_rating: number | null\n          management_rating: number | null\n          culture_rating: number | null\n          title: string\n          pros: string | null\n          cons: string | null\n          advice_to_management: string | null\n          job_title: string | null\n          employment_status: 'current' | 'former' | null\n          employment_duration: string | null\n          department: string | null\n          location: string | null\n          is_approved: boolean\n          is_featured: boolean\n          helpful_count: number\n          created_at: string\n          updated_at: string\n        }\n      }\n      salary_reports: {\n        Row: {\n          salary_id: string\n          company_id: string\n          anonymous_user_hash: string\n          job_title: string\n          department: string | null\n          location: string | null\n          experience_level: 'entry' | 'mid' | 'senior' | 'lead' | 'executive' | null\n          base_salary: number | null\n          bonus: number\n          stock_options: number\n          total_compensation: number | null\n          currency: string\n          employment_type: 'full-time' | 'part-time' | 'contract' | 'internship' | null\n          years_of_experience: number | null\n          years_at_company: number | null\n          is_approved: boolean\n          created_at: string\n          updated_at: string\n        }\n      }\n      company_flags: {\n        Row: {\n          flag_id: string\n          company_id: string\n          flag_type: 'red' | 'green' | null\n          flag_text: string\n          flag_count: number\n          created_at: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAIoB;AAJpB;AACA;AAAA;;;AAEA,yBAAyB;AACzB,MAAM;AACN,MAAM;AAEN,kFAAkF;AAClF,IAAI,wBAAuE;AAC3E,IAAI,uBAA+D;AAG5D,MAAM,8BAA8B;IACzC,uCAAmC;;IAGnC;IAEA,6BAA6B;IAC7B,IAAI,CAAC,uBAAuB;QAC1B,wBAAwB,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;IAC3D;IACA,OAAO;AACT;AAGO,MAAM,qBAAqB;IAChC,IAAI,CAAC,sBAAsB;QACzB,uBAAuB,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;IACnD;IACA,OAAO;AACT;AAGO,MAAM,WAAW", "debugId": null}}, {"offset": {"line": 1147, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Haq%20website%20v1/haq-frontend-nextjs/src/providers/SWRProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { SWRConfig } from 'swr'\nimport { createClientComponentClient } from '@/lib/supabase'\n\n// Default fetcher function for SWR\nconst fetcher = async (url: string) => {\n  const supabase = createClientComponentClient()\n  \n  // Handle different API endpoints\n  if (url.startsWith('/api/companies')) {\n    // For now, return mock data since we need to set up proper API routes\n    // The direct Supabase schema queries don't work with PostgREST syntax\n    return [\n      {\n        id: 1,\n        company_id: 1,\n        name: \"TechCorp Solutions\",\n        slug: \"techcorp-solutions\",\n        logo_url: \"/placeholder-company.svg\",\n        description: \"Leading technology solutions provider\",\n        location: \"Karachi, Pakistan\",\n        industry: \"Technology\",\n        employee_count: \"500-1000\",\n        haq_score: 85,\n        total_reviews: 24,\n        redFlags: [\"High turnover rate\", \"Unpaid overtime\"],\n        greenFlags: [\"Good benefits\", \"Career growth opportunities\"]\n      },\n      {\n        id: 2,\n        company_id: 2,\n        name: \"InnovatePak\",\n        slug: \"innovatepak\",\n        logo_url: \"/placeholder-company.svg\",\n        description: \"Innovation-driven software company\",\n        location: \"Lahore, Pakistan\",\n        industry: \"Software\",\n        employee_count: \"100-500\",\n        haq_score: 78,\n        total_reviews: 18,\n        redFlags: [\"Limited remote work\"],\n        greenFlags: [\"Modern office\", \"Learning opportunities\", \"Flexible hours\"]\n      },\n      {\n        id: 3,\n        company_id: 3,\n        name: \"DataFlow Systems\",\n        slug: \"dataflow-systems\",\n        logo_url: \"/placeholder-company.svg\",\n        description: \"Data analytics and business intelligence\",\n        location: \"Islamabad, Pakistan\",\n        industry: \"Analytics\",\n        employee_count: \"50-100\",\n        haq_score: 72,\n        total_reviews: 12,\n        redFlags: [\"Micromanagement\"],\n        greenFlags: [\"Good work-life balance\", \"Competitive salary\"]\n      }\n    ]\n  }\n  \n  if (url.startsWith('/api/company/')) {\n    const slug = url.split('/').pop()\n    // Return mock data for individual company\n    const companies = [\n      {\n        id: 1,\n        company_id: 1,\n        name: \"TechCorp Solutions\",\n        slug: \"techcorp-solutions\",\n        logo_url: \"/placeholder-company.svg\",\n        description: \"Leading technology solutions provider\",\n        location: \"Karachi, Pakistan\",\n        industry: \"Technology\",\n        employee_count: \"500-1000\",\n        haq_score: 85,\n        total_reviews: 24,\n        redFlags: [\"High turnover rate\", \"Unpaid overtime\"],\n        greenFlags: [\"Good benefits\", \"Career growth opportunities\"]\n      }\n    ]\n\n    return companies.find(company => company.slug === slug) || companies[0]\n  }\n  \n  // Default fetch for other endpoints\n  const response = await fetch(url)\n  if (!response.ok) {\n    throw new Error('Failed to fetch')\n  }\n  return response.json()\n}\n\n// SWR configuration\nconst swrConfig = {\n  fetcher,\n  revalidateOnFocus: false,\n  revalidateOnReconnect: true,\n  refreshInterval: 0, // Disable automatic refresh\n  errorRetryCount: 3,\n  errorRetryInterval: 5000,\n  onError: (error: Error) => {\n    console.error('SWR Error:', error)\n  },\n  onSuccess: (data: any, key: string) => {\n    // Optional: Log successful data fetches in development\n    if (process.env.NODE_ENV === 'development') {\n      console.log('SWR Success:', key, data)\n    }\n  }\n}\n\ninterface SWRProviderProps {\n  children: React.ReactNode\n}\n\nexport function SWRProvider({ children }: SWRProviderProps) {\n  return (\n    <SWRConfig value={swrConfig}>\n      {children}\n    </SWRConfig>\n  )\n}\n"], "names": [], "mappings": ";;;AA2GQ;;AAzGR;AACA;AAHA;;;;AAKA,mCAAmC;AACnC,MAAM,UAAU,OAAO;IACrB,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,8BAA2B,AAAD;IAE3C,iCAAiC;IACjC,IAAI,IAAI,UAAU,CAAC,mBAAmB;QACpC,sEAAsE;QACtE,sEAAsE;QACtE,OAAO;YACL;gBACE,IAAI;gBACJ,YAAY;gBACZ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,gBAAgB;gBAChB,WAAW;gBACX,eAAe;gBACf,UAAU;oBAAC;oBAAsB;iBAAkB;gBACnD,YAAY;oBAAC;oBAAiB;iBAA8B;YAC9D;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,gBAAgB;gBAChB,WAAW;gBACX,eAAe;gBACf,UAAU;oBAAC;iBAAsB;gBACjC,YAAY;oBAAC;oBAAiB;oBAA0B;iBAAiB;YAC3E;YACA;gBACE,IAAI;gBACJ,YAAY;gBACZ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,gBAAgB;gBAChB,WAAW;gBACX,eAAe;gBACf,UAAU;oBAAC;iBAAkB;gBAC7B,YAAY;oBAAC;oBAA0B;iBAAqB;YAC9D;SACD;IACH;IAEA,IAAI,IAAI,UAAU,CAAC,kBAAkB;QACnC,MAAM,OAAO,IAAI,KAAK,CAAC,KAAK,GAAG;QAC/B,0CAA0C;QAC1C,MAAM,YAAY;YAChB;gBACE,IAAI;gBACJ,YAAY;gBACZ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,gBAAgB;gBAChB,WAAW;gBACX,eAAe;gBACf,UAAU;oBAAC;oBAAsB;iBAAkB;gBACnD,YAAY;oBAAC;oBAAiB;iBAA8B;YAC9D;SACD;QAED,OAAO,UAAU,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,KAAK,SAAS,SAAS,CAAC,EAAE;IACzE;IAEA,oCAAoC;IACpC,MAAM,WAAW,MAAM,MAAM;IAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,SAAS,IAAI;AACtB;AAEA,oBAAoB;AACpB,MAAM,YAAY;IAChB;IACA,mBAAmB;IACnB,uBAAuB;IACvB,iBAAiB;IACjB,iBAAiB;IACjB,oBAAoB;IACpB,SAAS,CAAC;QACR,QAAQ,KAAK,CAAC,cAAc;IAC9B;IACA,WAAW,CAAC,MAAW;QACrB,uDAAuD;QACvD,wCAA4C;YAC1C,QAAQ,GAAG,CAAC,gBAAgB,KAAK;QACnC;IACF;AACF;AAMO,SAAS,YAAY,EAAE,QAAQ,EAAoB;IACxD,qBACE,6LAAC,iKAAA,CAAA,YAAS;QAAC,OAAO;kBACf;;;;;;AAGP;KANgB", "debugId": null}}]}