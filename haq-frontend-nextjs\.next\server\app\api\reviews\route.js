(()=>{var e={};e.id=217,e.ids=[217],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12909:(e,t,s)=>{"use strict";s.d(t,{DU:()=>c,Qi:()=>l,h8:()=>u,ne:()=>d});var r=s(85663),a=s(43205),n=s.n(a);let i=()=>{let e=process.env.JWT_SECRET;if(!e)throw Error("JWT_SECRET environment variable is required");return e},o=process.env.JWT_EXPIRES_IN||"7d";class u{static{this.SALT_ROUNDS=12}static async hashPassword(e){try{let t=await r.Ay.genSalt(this.SALT_ROUNDS);return await r.Ay.hash(e,t)}catch(e){throw Error("Failed to hash password")}}static async verifyPassword(e,t){try{return await r.Ay.compare(e,t)}catch(e){throw Error("Failed to verify password")}}}class c{static generateToken(e){try{return n().sign(e,i(),{expiresIn:o,algorithm:"HS256"})}catch(e){throw Error("Failed to generate JWT token")}}static verifyToken(e){try{return n().verify(e,i(),{algorithms:["HS256"]})}catch(e){if(e instanceof n().TokenExpiredError)throw Error("Token has expired");if(e instanceof n().JsonWebTokenError)throw Error("Invalid token");throw Error("Token verification failed")}}static extractTokenFromHeader(e){return e&&e.startsWith("Bearer ")?e.substring(7):null}}let l={NAME:"haq_auth_token",OPTIONS:{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:604800,path:"/"}};class d{static isValidEmail(e){return/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)&&e.length<=255}static isValidUsername(e){return/^[a-zA-Z0-9_]{3,50}$/.test(e)}static validatePassword(e){let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),e.length>128&&t.push("Password must be less than 128 characters"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[0-9]/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e)||t.push("Password must contain at least one special character"),{isValid:0===t.length,errors:t}}static sanitizeInput(e){return e.trim().replace(/[<>]/g,"").substring(0,1e3)}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32325:e=>{"use strict";e.exports=require("jsdom")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51641:(e,t,s)=>{"use strict";s.d(t,{L:()=>i,b:()=>o});var r=s(44999),a=s(12909),n=s(56621);class i{static async setAuthCookie(e){(await (0,r.UL)()).set(a.Qi.NAME,e,a.Qi.OPTIONS)}static async getAuthToken(){try{let e=(await (0,r.UL)()).get(a.Qi.NAME);return e?.value||null}catch(e){return null}}static async removeAuthCookie(){(await (0,r.UL)()).delete(a.Qi.NAME)}}class o{static async getCurrentUser(){try{let e=await i.getAuthToken();if(!e)return null;let t=a.DU.verifyToken(e),{data:s,error:r}=await n.ND.from("haq_users_db.users").select("user_id, username, email, role, created_at, updated_at").eq("user_id",t.user_id).limit(1);if(r||!s||0===s.length)return null;return s[0]}catch(e){return null}}static async isAuthenticated(){return null!==await this.getCurrentUser()}static async isAdmin(){try{let e=await i.getAuthToken();if(!e)return!1;let t=a.DU.verifyToken(e);return"admin"===t.role}catch(e){return!1}}static async verifyTokenAndGetUser(e){try{let t=a.DU.verifyToken(e),{data:s,error:r}=await n.ND.from("haq_users_db.users").select("user_id, username, email, role, created_at, updated_at").eq("user_id",t.user_id).limit(1);if(r||!s||0===s.length)return null;return s[0]}catch(e){return null}}}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,s)=>{"use strict";s.d(t,{ND:()=>n});var r=s(39398);s(98766);let a=null,n=a=(0,r.createClient)("https://wqbuilazpyxpwyuwuqpi.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndxYnVpbGF6cHl4cHd5dXd1cXBpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1NTYyNDMsImV4cCI6MjA2NjEzMjI0M30.GeRI54Rskwdbfm9_lRxy1-7YQ8vA74JWblNF2GRpzrI")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81586:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>R,routeModule:()=>E,serverHooks:()=>k,workAsyncStorage:()=>N,workUnitAsyncStorage:()=>q});var r={};s.r(r),s.d(r,{DELETE:()=>T,GET:()=>x,PATCH:()=>b,POST:()=>_,PUT:()=>A});var a=s(96559),n=s(48088),i=s(37719),o=s(32190),u=s(51641),c=s(56621),l=s(45697),d=s(39296),p=s(32325),m=s(55511),h=s.n(m);let w=new p.JSDOM("").window,g=(0,d.A)(w),y=l.z.object({company_id:l.z.string().uuid("Invalid company ID format"),overall_rating:l.z.number().int().min(1,"Rating must be at least 1").max(5,"Rating must be at most 5"),pros:l.z.string().optional(),cons:l.z.string().optional(),advice_management:l.z.string().optional()});function v(e){return e&&"string"==typeof e&&g.sanitize(e,{ALLOWED_TAGS:[],ALLOWED_ATTR:[],KEEP_CONTENT:!0}).trim()||null}function f(e){let t=e.toLowerCase(),s=[];for(let e of["manager","ceo","director","supervisor","boss","lead","john","jane","smith","johnson","williams","brown","jones","email","phone","address","linkedin","facebook","twitter","my name","i am","called me","told me personally"])t.includes(e)&&s.push(e);return s}async function _(e){try{if(!await u.b.isAuthenticated())return o.NextResponse.json({success:!1,message:"Authentication required to submit reviews"},{status:401});let t=await u.b.getCurrentUser();if(!t)return o.NextResponse.json({success:!1,message:"Failed to retrieve user information"},{status:401});let s=await e.json(),r=y.safeParse(s);if(!r.success)return o.NextResponse.json({success:!1,message:"Validation failed",errors:r.error.errors},{status:400});let a=r.data,{data:n,error:i}=await c.ND.from("haq_content_db.companies").select("company_id, name").eq("company_id",a.company_id).single();if(i||!n)return o.NextResponse.json({success:!1,message:"Company not found"},{status:404});let l=v(a.pros),d=v(a.cons),p=v(a.advice_management),m=[];if(l){let e=f(l);m.push(...e.map(e=>`pros: ${e}`))}if(d){let e=f(d);m.push(...e.map(e=>`cons: ${e}`))}if(p){let e=f(p);m.push(...e.map(e=>`advice: ${e}`))}let w=function(e,t){let s=process.env.ANONYMOUS_HASH_SECRET||"default-secret-change-in-production",r=`${e}:${t}:${s}`;return h().createHash("sha256").update(r).digest("hex").substring(0,16)}(t.user_id,a.company_id),{data:g,error:_}=await c.ND.from("haq_content_db.reviews").insert({company_id:a.company_id,anonymous_user_hash:w,overall_rating:a.overall_rating,title:"Anonymous Review",pros:l,cons:d,advice_to_management:p,is_approved:!1}).select().single();if(_)return console.error("Error creating review:",_),o.NextResponse.json({success:!1,message:"Failed to submit review"},{status:500});let x={success:!0,message:"Review submitted successfully and is pending moderation",data:{review_id:g.review_id,company_name:n.name,status:"pending",submitted_at:g.created_at}};return m.length>0&&(x.warnings={pii_detected:m,message:"Potential personally identifiable information detected. Please review your submission."}),o.NextResponse.json(x,{status:201})}catch(e){return console.error("Review submission error:",e),o.NextResponse.json({success:!1,message:"Internal server error"},{status:500})}}async function x(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"POST"}})}async function A(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"POST"}})}async function T(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"POST"}})}async function b(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"POST"}})}let E=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/reviews/route",pathname:"/api/reviews",filename:"route",bundlePath:"app/api/reviews/route"},resolvedPagePath:"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\reviews\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:N,workUnitAsyncStorage:q,serverHooks:k}=E;function R(){return(0,i.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:q})}},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580,573,358,697,296],()=>s(81586));module.exports=r})();