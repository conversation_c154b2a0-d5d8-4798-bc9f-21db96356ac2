import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { ValidationUtils } from '@/lib/sanitization';

/**
 * GET /api/companies/[id]/reviews
 * Public endpoint to get approved reviews for a company
 * Following HAQ-rules.md PUB-04 specification
 * 
 * CRITICAL SECURITY REQUIREMENT (RULE-601):
 * - author_id MUST NEVER be included in response
 * - Only approved reviews are returned
 * - Anonymity protection is MANDATORY
 * 
 * Path Parameters:
 * - id: Company UUID
 * 
 * Query Parameters:
 * - page: Page number for pagination (default: 1)
 * - limit: Number of results per page (default: 10, max: 50)
 * - sort: Sort order - 'newest', 'oldest', 'highest_rated', 'lowest_rated' (default: 'newest')
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const companyId = params.id;
    const { searchParams } = new URL(request.url);

    // Validate UUID format
    if (!ValidationUtils.isValidUUID(companyId)) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Invalid company ID format',
          error: 'Company ID must be a valid UUID'
        },
        { status: 400 }
      );
    }

    // Parse and validate query parameters
    const page = Math.max(1, parseInt(searchParams.get('page') || '1'));
    const limit = Math.min(50, Math.max(1, parseInt(searchParams.get('limit') || '10')));
    const sort = searchParams.get('sort') || 'newest';
    
    // Validate sort parameter
    const validSortOptions = ['newest', 'oldest', 'highest_rated', 'lowest_rated'];
    if (!validSortOptions.includes(sort)) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Invalid sort parameter',
          error: `Sort must be one of: ${validSortOptions.join(', ')}`
        },
        { status: 400 }
      );
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Verify company exists
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('company_id, name')
      .eq('company_id', companyId)
      .single();

    if (companyError || !company) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Company not found',
          error: 'The requested company does not exist'
        },
        { status: 404 }
      );
    }

    // Build query for approved reviews from content schema (RULE-102)
    // CRITICAL: anonymous_user_hash is NEVER selected to maintain anonymity (RULE-601)
    let query = supabase
      .from('haq_content_db.reviews')
      .select(`
        review_id,
        overall_rating,
        pros,
        cons,
        advice_to_management,
        created_at
      `) // NOTICE: anonymous_user_hash is DELIBERATELY EXCLUDED
      .eq('company_id', companyId)
      .eq('is_approved', true); // Only approved reviews

    // Apply sorting
    switch (sort) {
      case 'newest':
        query = query.order('created_at', { ascending: false });
        break;
      case 'oldest':
        query = query.order('created_at', { ascending: true });
        break;
      case 'highest_rated':
        query = query.order('overall_rating', { ascending: false }).order('created_at', { ascending: false });
        break;
      case 'lowest_rated':
        query = query.order('overall_rating', { ascending: true }).order('created_at', { ascending: false });
        break;
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: reviews, error: reviewsError } = await query;

    if (reviewsError) {
      console.error('Error fetching reviews:', reviewsError);
      return NextResponse.json(
        { 
          success: false, 
          message: 'Failed to fetch reviews',
          error: 'Database query failed'
        },
        { status: 500 }
      );
    }

    // Get total count of approved reviews for pagination metadata
    const { count: totalCount, error: countError } = await supabase
      .from('haq_content_db.reviews')
      .select('*', { count: 'exact', head: true })
      .eq('company_id', companyId)
      .eq('is_approved', true);

    if (countError) {
      console.error('Error counting reviews:', countError);
      return NextResponse.json(
        { 
          success: false, 
          message: 'Failed to count reviews',
          error: 'Database query failed'
        },
        { status: 500 }
      );
    }

    // Format reviews for response (additional anonymity protection)
    const formattedReviews = (reviews || []).map(review => ({
      review_id: review.review_id,
      overall_rating: review.overall_rating,
      pros: review.pros,
      cons: review.cons,
      advice_to_management: review.advice_to_management,
      created_at: review.created_at,
      // CRITICAL: anonymous_user_hash is NEVER included in response
      // This ensures complete anonymity protection as per RULE-601
    }));

    // Return paginated response
    return NextResponse.json({
      success: true,
      data: {
        company: {
          company_id: company.company_id,
          name: company.name
        },
        reviews: formattedReviews,
        pagination: {
          page,
          limit,
          total: totalCount || 0,
          totalPages: Math.ceil((totalCount || 0) / limit),
          hasNext: page * limit < (totalCount || 0),
          hasPrev: page > 1
        },
        sort,
        filters: {
          status: 'approved' // Only approved reviews are shown
        }
      }
    }, {
      headers: {
        // CDN_SHORT caching policy as per HAQ-rules.md
        'Cache-Control': 'public, max-age=300, s-maxage=300', // 5 minutes
        'Vary': 'Accept-Encoding',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY'
      }
    });

  } catch (error) {
    console.error('Company reviews API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error',
        error: 'Unexpected error occurred'
      },
      { status: 500 }
    );
  }
}

/**
 * Handle unsupported HTTP methods
 */
export async function POST() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405, headers: { 'Allow': 'GET' } }
  );
}
