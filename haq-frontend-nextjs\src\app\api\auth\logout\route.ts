import { NextRequest, NextResponse } from 'next/server';
import { ServerCookieUtils } from '@/lib/auth-server';

interface LogoutResponse {
  success: boolean;
  message: string;
}

export async function POST(_request: NextRequest): Promise<NextResponse<LogoutResponse>> {
  try {
    // ANONYMITY PROTECTION: No user identification in logs per RULE-601
    // Simply clear the authentication cookie without logging user details

    // Remove authentication cookie
    await ServerCookieUtils.removeAuthCookie();

    return NextResponse.json(
      {
        success: true,
        message: 'Logout successful'
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Logout error:', error);
    
    // Even if there's an error, try to clear the cookie
    try {
      await ServerCookieUtils.removeAuthCookie();
    } catch (cookieError) {
      console.error('Failed to clear auth cookie:', cookieError);
    }

    return NextResponse.json(
      { success: false, message: 'Logout failed' },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}
