(()=>{var e={};e.id=198,e.ids=[198],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28370:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(60687),a=s(43210),i=s(78122),n=s(5336),d=s(35071),o=s(43649);function c(){let[e,t]=(0,a.useState)([]),[s,c]=(0,a.useState)(!1),l=async()=>{c(!0),t([]);let e=[];for(let t of[{name:"Admin Login Test",test:async()=>{let e=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:"admin",password:"admin123"})});return{success:e.ok,data:await e.json()}}},{name:"Pending Reviews API Test",test:async()=>{let e=await fetch("/api/admin/reviews/pending?page=1&limit=5");return{success:e.ok,data:await e.json()}}},{name:"Unauthorized Access Test",test:async()=>{document.cookie.split(";").forEach(function(e){document.cookie=e.replace(/^ +/,"").replace(/=.*/,"=;expires="+new Date().toUTCString()+";path=/")});let e=await fetch("/api/admin/reviews/pending");return{success:401===e.status,data:await e.json()}}},{name:"Invalid Review ID Test",test:async()=>{let e=await fetch("/api/admin/reviews/invalid-id/status",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:"approved"})});return{success:404===e.status||400===e.status,data:await e.json()}}},{name:"Invalid Status Test",test:async()=>{let e=await fetch("/api/admin/reviews/test-id/status",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:"invalid_status"})});return{success:400===e.status,data:await e.json()}}}])try{console.log(`Running ${t.name}...`);let s=await t.test();e.push({name:t.name,success:s.success,data:s.data,error:null})}catch(s){e.push({name:t.name,success:!1,data:null,error:s.message})}t(e),c(!1)};return(0,r.jsx)("div",{className:"min-h-screen bg-background-primary p-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-text-primary mb-8",children:"Admin Moderation Testing"}),(0,r.jsxs)("div",{className:"bg-surface-primary border border-border-primary rounded-lg p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"API Tests"}),(0,r.jsx)("div",{className:"flex space-x-4 mb-6",children:(0,r.jsxs)("button",{onClick:l,disabled:s,className:"bg-accent-primary hover:bg-accent-secondary text-text-on-accent px-6 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50 flex items-center space-x-2",children:[s&&(0,r.jsx)(i.A,{className:"w-4 h-4 animate-spin"}),(0,r.jsx)("span",{children:"Run API Tests"})]})}),e.length>0&&(0,r.jsx)("div",{className:"space-y-3",children:e.map((e,t)=>(0,r.jsxs)("div",{className:`p-4 rounded-lg border ${e.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[e.success?(0,r.jsx)(n.A,{className:"w-5 h-5 text-green-600"}):(0,r.jsx)(d.A,{className:"w-5 h-5 text-red-600"}),(0,r.jsx)("span",{className:`font-medium ${e.success?"text-green-800":"text-red-800"}`,children:e.name})]}),e.error&&(0,r.jsxs)("p",{className:"mt-2 text-sm text-red-600",children:["Error: ",e.error]}),e.data&&(0,r.jsxs)("details",{className:"mt-2",children:[(0,r.jsx)("summary",{className:"text-sm text-gray-600 cursor-pointer",children:"View Response"}),(0,r.jsx)("pre",{className:"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto",children:JSON.stringify(e.data,null,2)})]})]},t))})]}),(0,r.jsxs)("div",{className:"bg-surface-primary border border-border-primary rounded-lg p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"UI Tests"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-text-primary mb-2",children:"Admin Dashboard"}),(0,r.jsx)("button",{onClick:()=>{window.open("/admin","_blank")},className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200",children:"Open Admin Dashboard"}),(0,r.jsx)("p",{className:"text-sm text-text-secondary mt-2",children:"Test: Navigate to admin dashboard and verify moderation queue link"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-text-primary mb-2",children:"Moderation Queue"}),(0,r.jsx)("button",{onClick:()=>{window.open("/admin/moderation","_blank")},className:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors duration-200",children:"Open Moderation Queue"}),(0,r.jsx)("p",{className:"text-sm text-text-secondary mt-2",children:"Test: Open moderation queue and verify pending reviews display"})]})]})]}),(0,r.jsxs)("div",{className:"bg-surface-primary border border-border-primary rounded-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"Manual Testing Checklist"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",className:"mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-text-primary",children:"Admin Authentication"}),(0,r.jsx)("p",{className:"text-sm text-text-secondary",children:"Verify only admin users can access moderation queue"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",className:"mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-text-primary",children:"Pending Reviews Display"}),(0,r.jsx)("p",{className:"text-sm text-text-secondary",children:"Check that pending reviews are displayed with all required information"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",className:"mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-text-primary",children:"Approve Functionality"}),(0,r.jsx)("p",{className:"text-sm text-text-secondary",children:"Test approve button with confirmation dialog"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",className:"mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-text-primary",children:"Reject Functionality"}),(0,r.jsx)("p",{className:"text-sm text-text-secondary",children:"Test reject button with confirmation dialog"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",className:"mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-text-primary",children:"Pagination"}),(0,r.jsx)("p",{className:"text-sm text-text-secondary",children:"Verify pagination works correctly for multiple pages"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",className:"mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-text-primary",children:"Error Handling"}),(0,r.jsx)("p",{className:"text-sm text-text-secondary",children:"Test error scenarios and verify proper error messages"})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("input",{type:"checkbox",className:"mt-1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-text-primary",children:"Real-time Updates"}),(0,r.jsx)("p",{className:"text-sm text-text-secondary",children:"Verify reviews disappear from queue after approval/rejection"})]})]})]})]}),(0,r.jsx)("div",{className:"mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(o.A,{className:"w-5 h-5 text-yellow-600"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-yellow-800",children:"Testing Instructions"}),(0,r.jsxs)("p",{className:"text-sm text-yellow-700 mt-1",children:["1. Make sure the dev server is running on localhost:3000",(0,r.jsx)("br",{}),"2. Ensure you have admin credentials (username: admin, password: admin123)",(0,r.jsx)("br",{}),"3. Create some pending reviews first using the review submission form",(0,r.jsx)("br",{}),"4. Run API tests to verify backend functionality",(0,r.jsx)("br",{}),"5. Use UI tests to verify frontend functionality",(0,r.jsx)("br",{}),"6. Complete manual testing checklist"]})]})]})})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35071:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},43649:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55824:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Haq website v1\\\\haq-frontend-nextjs\\\\src\\\\app\\\\test-moderation\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\test-moderation\\page.tsx","default")},59975:(e,t,s)=>{Promise.resolve().then(s.bind(s,28370))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},74174:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>l,routeModule:()=>p,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),d=s(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);s.d(t,o);let c={children:["",{children:["test-moderation",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,55824)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\test-moderation\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,82893)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\test-moderation\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-moderation/page",pathname:"/test-moderation",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},76847:(e,t,s)=>{Promise.resolve().then(s.bind(s,55824))},78122:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,942,658,647],()=>s(74174));module.exports=r})();