(()=>{var e={};e.id=698,e.ids=[698],e.modules={1132:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Haq website v1\\\\haq-frontend-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10022:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12454:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(60687);t(43210);var a=t(59365),i=t(17313),n=t(10022),l=t(41312),d=t(25541);let c=()=>{let e=[{name:"Total Companies",value:"12",change:"+2",changeType:"increase",icon:i.A},{name:"Pending Reviews",value:"8",change:"+3",changeType:"increase",icon:n.A},{name:"Total Users",value:"156",change:"+12",changeType:"increase",icon:l.A},{name:"Platform Growth",value:"24%",change:"+5%",changeType:"increase",icon:d.A}],s=[{id:1,type:"company",message:'New company "TechFlow Solutions" added',time:"2 hours ago"},{id:2,type:"review",message:'Review submitted for "DataCorp Inc"',time:"4 hours ago"},{id:3,type:"user",message:"New user registration: <EMAIL>",time:"6 hours ago"},{id:4,type:"review",message:'Review approved for "InnovateTech"',time:"8 hours ago"}];return(0,r.jsx)(a.A,{title:"Dashboard",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:e.map(e=>(0,r.jsxs)("div",{className:"relative bg-white pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden",children:[(0,r.jsxs)("dt",{children:[(0,r.jsx)("div",{className:"absolute bg-blue-500 rounded-md p-3",children:(0,r.jsx)(e.icon,{className:"h-6 w-6 text-white","aria-hidden":"true"})}),(0,r.jsx)("p",{className:"ml-16 text-sm font-medium text-gray-500 truncate",children:e.name})]}),(0,r.jsxs)("dd",{className:"ml-16 pb-6 flex items-baseline sm:pb-7",children:[(0,r.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:e.value}),(0,r.jsx)("p",{className:`ml-2 flex items-baseline text-sm font-semibold ${"increase"===e.changeType?"text-green-600":"text-red-600"}`,children:e.change})]})]},e.name))}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,r.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:"Recent Activity"}),(0,r.jsx)("div",{className:"flow-root",children:(0,r.jsx)("ul",{className:"-mb-8",children:s.map((e,t)=>(0,r.jsx)("li",{children:(0,r.jsxs)("div",{className:"relative pb-8",children:[t!==s.length-1?(0,r.jsx)("span",{className:"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200","aria-hidden":"true"}):null,(0,r.jsxs)("div",{className:"relative flex space-x-3",children:[(0,r.jsx)("div",{children:(0,r.jsx)("span",{className:`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${"company"===e.type?"bg-blue-500":"review"===e.type?"bg-green-500":"bg-purple-500"}`,children:"company"===e.type?(0,r.jsx)(i.A,{className:"h-4 w-4 text-white"}):"review"===e.type?(0,r.jsx)(n.A,{className:"h-4 w-4 text-white"}):(0,r.jsx)(l.A,{className:"h-4 w-4 text-white"})})}),(0,r.jsxs)("div",{className:"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4",children:[(0,r.jsx)("div",{children:(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.message})}),(0,r.jsx)("div",{className:"text-right text-sm whitespace-nowrap text-gray-500",children:(0,r.jsx)("time",{children:e.time})})]})]})]})},e.id))})})]})}),(0,r.jsx)("div",{className:"bg-white shadow rounded-lg",children:(0,r.jsxs)("div",{className:"px-4 py-5 sm:p-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3",children:[(0,r.jsxs)("a",{href:"/admin/companies",className:"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 border border-gray-200 rounded-lg hover:border-gray-300",children:[(0,r.jsx)("div",{children:(0,r.jsx)("span",{className:"rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white",children:(0,r.jsx)(i.A,{className:"h-6 w-6"})})}),(0,r.jsxs)("div",{className:"mt-8",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium",children:[(0,r.jsx)("span",{className:"absolute inset-0","aria-hidden":"true"}),"Manage Companies"]}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Add, edit, and manage company profiles"})]})]}),(0,r.jsxs)("a",{href:"/admin/moderation",className:"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 border border-gray-200 rounded-lg hover:border-gray-300",children:[(0,r.jsx)("div",{children:(0,r.jsx)("span",{className:"rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white",children:(0,r.jsx)(n.A,{className:"h-6 w-6"})})}),(0,r.jsxs)("div",{className:"mt-8",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium",children:[(0,r.jsx)("span",{className:"absolute inset-0","aria-hidden":"true"}),"Review Moderation Queue"]}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"Approve or reject pending reviews"})]})]}),(0,r.jsxs)("a",{href:"/admin/users",className:"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 border border-gray-200 rounded-lg hover:border-gray-300",children:[(0,r.jsx)("div",{children:(0,r.jsx)("span",{className:"rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white",children:(0,r.jsx)(l.A,{className:"h-6 w-6"})})}),(0,r.jsxs)("div",{className:"mt-8",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium",children:[(0,r.jsx)("span",{className:"absolute inset-0","aria-hidden":"true"}),"User Management"]}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"View and manage user accounts"})]})]})]})]})})]})})}},17313:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59365:(e,s,t)=>{"use strict";t.d(s,{A:()=>j});var r=t(60687),a=t(43210),i=t.n(a),n=t(85814),l=t.n(n),d=t(16189),c=t(63213);let o=(0,t(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var m=t(10022),x=t(17313),h=t(41312),p=t(84027),u=t(99891),g=t(11860),f=t(40083),v=t(12941);let j=({children:e,title:s="Admin Dashboard"})=>{let[t,n]=(0,a.useState)(!1),{user:j,logout:b}=(0,c.As)(),y=(0,d.useRouter)(),N=j?.role==="admin";i().useEffect(()=>{j&&!N&&y.push("/")},[j,N,y]);let w=async()=>{await b(),y.push("/")},A=[{name:"Dashboard",href:"/admin",icon:o,current:!1},{name:"Moderation Queue",href:"/admin/moderation",icon:m.A,current:!1},{name:"Companies",href:"/admin/companies",icon:x.A,current:!1},{name:"Users",href:"/admin/users",icon:h.A,current:!1},{name:"Settings",href:"/admin/settings",icon:p.A,current:!1}];return N?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsxs)("div",{className:`fixed inset-0 z-40 lg:hidden ${t?"block":"hidden"}`,children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>n(!1)}),(0,r.jsxs)("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-white",children:[(0,r.jsx)("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:(0,r.jsx)("button",{type:"button",className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>n(!1),children:(0,r.jsx)(g.A,{className:"h-6 w-6 text-white"})})}),(0,r.jsxs)("div",{className:"flex-1 h-0 pt-5 pb-4 overflow-y-auto",children:[(0,r.jsx)("div",{className:"flex-shrink-0 flex items-center px-4",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"HAQ Admin"})}),(0,r.jsx)("nav",{className:"mt-5 px-2 space-y-1",children:A.map(e=>(0,r.jsxs)(l(),{href:e.href,className:"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900",children:[(0,r.jsx)(e.icon,{className:"mr-4 h-6 w-6"}),e.name]},e.name))})]}),(0,r.jsx)("div",{className:"flex-shrink-0 flex border-t border-gray-200 p-4",children:(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)("div",{className:"ml-3",children:[(0,r.jsx)("p",{className:"text-base font-medium text-gray-700",children:j?.username}),(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Admin"})]})})})]})]}),(0,r.jsx)("div",{className:"hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0",children:(0,r.jsxs)("div",{className:"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white",children:[(0,r.jsxs)("div",{className:"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto",children:[(0,r.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"HAQ Admin"})}),(0,r.jsx)("nav",{className:"mt-5 flex-1 px-2 bg-white space-y-1",children:A.map(e=>(0,r.jsxs)(l(),{href:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900",children:[(0,r.jsx)(e.icon,{className:"mr-3 h-5 w-5"}),e.name]},e.name))})]}),(0,r.jsx)("div",{className:"flex-shrink-0 flex border-t border-gray-200 p-4",children:(0,r.jsxs)("div",{className:"flex items-center w-full",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-700",children:j?.username}),(0,r.jsx)("p",{className:"text-xs font-medium text-gray-500",children:"Admin"})]}),(0,r.jsx)("button",{onClick:w,className:"ml-3 flex items-center justify-center h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500",title:"Logout",children:(0,r.jsx)(f.A,{className:"h-4 w-4"})})]})})]})}),(0,r.jsxs)("div",{className:"lg:pl-64 flex flex-col flex-1",children:[(0,r.jsx)("div",{className:"sticky top-0 z-10 lg:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50",children:(0,r.jsx)("button",{type:"button",className:"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",onClick:()=>n(!0),children:(0,r.jsx)(v.A,{className:"h-6 w-6"})})}),(0,r.jsx)("div",{className:"bg-white shadow",children:(0,r.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"py-6",children:(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:s})})})}),(0,r.jsx)("main",{className:"flex-1",children:(0,r.jsx)("div",{className:"py-6",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e})})})]})]}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(u.A,{className:"mx-auto h-12 w-12 text-red-500 mb-4"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this area."}),(0,r.jsx)(l(),{href:"/",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"Return to Home"})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73323:(e,s,t)=>{Promise.resolve().then(t.bind(t,12454))},74075:e=>{"use strict";e.exports=require("zlib")},78899:(e,s,t)=>{Promise.resolve().then(t.bind(t,1132))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80358:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1132)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,82893)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\admin\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,942,658,647],()=>t(80358));module.exports=r})();