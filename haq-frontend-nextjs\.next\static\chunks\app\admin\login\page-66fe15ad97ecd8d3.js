(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[116],{283:(e,s,t)=>{"use strict";t.d(s,{As:()=>d,AuthProvider:()=>c});var a=t(5155),r=t(2115);let l={user:null,isLoading:!0,isAuthenticated:!1,error:null};function i(e,s){switch(s.type){case"AUTH_START":return{...e,isLoading:!0,error:null};case"AUTH_SUCCESS":return{...e,user:s.payload,isAuthenticated:!0,isLoading:!1,error:null};case"AUTH_FAILURE":return{...e,user:null,isAuthenticated:!1,isLoading:!1,error:s.payload};case"AUTH_LOGOUT":return{...e,user:null,isAuthenticated:!1,isLoading:!1,error:null};case"CLEAR_ERROR":return{...e,error:null};case"SET_LOADING":return{...e,isLoading:s.payload};default:return e}}let n=(0,r.createContext)(null);function c(e){let{children:s}=e,[t,c]=(0,r.useReducer)(i,l);(0,r.useEffect)(()=>{d()},[]);let d=(0,r.useCallback)(async()=>{try{c({type:"SET_LOADING",payload:!0});let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});if(e.ok){let s=await e.json();s.success&&s.user?c({type:"AUTH_SUCCESS",payload:s.user}):c({type:"AUTH_LOGOUT"})}else c({type:"AUTH_LOGOUT"})}catch(e){c({type:"AUTH_LOGOUT"})}finally{c({type:"SET_LOADING",payload:!1})}},[]),o=(0,r.useCallback)(async(e,s)=>{try{c({type:"AUTH_START"});let t=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:s})}),a=await t.json();if(a.success&&a.user)return c({type:"AUTH_SUCCESS",payload:a.user}),{success:!0,message:a.message};return c({type:"AUTH_FAILURE",payload:a.message}),{success:!1,message:a.message}}catch(s){let e="Network error. Please try again.";return c({type:"AUTH_FAILURE",payload:e}),{success:!1,message:e}}},[]),u=(0,r.useCallback)(async(e,s,t)=>{try{c({type:"AUTH_START"});let a=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({username:e,email:s,password:t})}),r=await a.json();if(r.success&&r.user)return c({type:"AUTH_SUCCESS",payload:r.user}),{success:!0,message:r.message};return c({type:"AUTH_FAILURE",payload:r.message}),{success:!1,message:r.message}}catch(s){let e="Network error. Please try again.";return c({type:"AUTH_FAILURE",payload:e}),{success:!1,message:e}}},[]),m=(0,r.useCallback)(async()=>{try{c({type:"SET_LOADING",payload:!0}),await fetch("/api/auth/logout",{method:"POST",credentials:"include"}),c({type:"AUTH_LOGOUT"})}catch(e){c({type:"AUTH_LOGOUT"})}finally{c({type:"SET_LOADING",payload:!1})}},[]),h=(0,r.useCallback)(()=>{c({type:"CLEAR_ERROR"})},[]),x=(0,r.useCallback)(async()=>{await d()},[d]),y=(0,r.useMemo)(()=>({...t,login:o,register:u,logout:m,clearError:h,refreshUser:x}),[t,o,u,m,h,x]);return(0,a.jsx)(n.Provider,{value:y,children:s})}function d(){let e=(0,r.useContext)(n);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},4450:(e,s,t)=>{Promise.resolve().then(t.bind(t,9173))},5339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5525:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},9173:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(5155),r=t(2115),l=t(5695),i=t(283),n=t(5525),c=t(5339),d=t(9946);let o=(0,d.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),u=(0,d.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var m=t(6874),h=t.n(m);let x=()=>{let[e,s]=(0,r.useState)(""),[t,d]=(0,r.useState)(""),[m,x]=(0,r.useState)(!1),[y,p]=(0,r.useState)(!1),[f,g]=(0,r.useState)(""),{login:b,user:A,isAuthenticated:w}=(0,i.As)(),j=(0,l.useRouter)(),N=(0,l.useSearchParams)(),v=N.get("returnUrl")||"/admin",T=N.get("error");(0,r.useEffect)(()=>{"admin_required"===T&&g("Admin access required. Please login with an admin account.")},[T]),(0,r.useEffect)(()=>{w&&(null==A?void 0:A.role)==="admin"?j.push(v):w&&(null==A?void 0:A.role)!=="admin"&&g("Your account does not have admin privileges.")},[w,A,j,v]);let k=async s=>{s.preventDefault(),g(""),p(!0);try{let s=await b(e,t);s.success||g(s.message||"Login failed")}catch(e){g("An unexpected error occurred")}finally{p(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full",children:(0,a.jsx)(n.A,{className:"w-8 h-8 text-white"})})}),(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Admin Access"}),(0,a.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Sign in to access the admin dashboard"})]}),(0,a.jsxs)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:[(0,a.jsxs)("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[(0,a.jsxs)("form",{className:"space-y-6",onSubmit:k,children:[f&&(0,a.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(c.A,{className:"h-5 w-5 text-red-400"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Authentication Error"}),(0,a.jsx)("div",{className:"mt-2 text-sm text-red-700",children:(0,a.jsx)("p",{children:f})})]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>s(e.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your admin email"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,a.jsxs)("div",{className:"mt-1 relative",children:[(0,a.jsx)("input",{id:"password",name:"password",type:m?"text":"password",autoComplete:"current-password",required:!0,value:t,onChange:e=>d(e.target.value),className:"appearance-none block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Enter your password"}),(0,a.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>x(!m),children:m?(0,a.jsx)(o,{className:"h-5 w-5 text-gray-400"}):(0,a.jsx)(u,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:y,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:y?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign in to Admin"})})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Need help?"})})]}),(0,a.jsx)("div",{className:"mt-6 text-center",children:(0,a.jsx)(h(),{href:"/",className:"text-sm text-blue-600 hover:text-blue-500",children:"Return to main site"})})]})]}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(c.A,{className:"h-5 w-5 text-yellow-400"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Admin Access Only"}),(0,a.jsx)("div",{className:"mt-2 text-sm text-yellow-700",children:(0,a.jsx)("p",{children:"This area is restricted to administrators only. Regular user accounts cannot access admin features."})})]})]})})})]})]})}},9946:(e,s,t)=>{"use strict";t.d(s,{A:()=>u});var a=t(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=function(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return s.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim()},c=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,s)=>{let{color:t="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:o="",children:u,iconNode:m,...h}=e;return(0,a.createElement)("svg",{ref:s,...d,width:r,height:r,stroke:t,strokeWidth:i?24*Number(l)/Number(r):l,className:n("lucide",o),...!u&&!c(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[s,t]=e;return(0,a.createElement)(s,t)}),...Array.isArray(u)?u:[u]])}),u=(e,s)=>{let t=(0,a.forwardRef)((t,l)=>{let{className:c,...d}=t;return(0,a.createElement)(o,{ref:l,iconNode:s,className:n("lucide-".concat(r(i(e))),"lucide-".concat(e),c),...d})});return t.displayName=i(e),t}}},e=>{var s=s=>e(e.s=s);e.O(0,[244,441,684,358],()=>s(4450)),_N_E=e.O()}]);