(()=>{var e={};e.id=819,e.ids=[819],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12909:(e,t,r)=>{"use strict";r.d(t,{DU:()=>c,Qi:()=>l,h8:()=>u,ne:()=>d});var s=r(85663),a=r(43205),n=r.n(a);let i=()=>{let e=process.env.JWT_SECRET;if(!e)throw Error("JWT_SECRET environment variable is required");return e},o=process.env.JWT_EXPIRES_IN||"7d";class u{static{this.SALT_ROUNDS=12}static async hashPassword(e){try{let t=await s.Ay.genSalt(this.SALT_ROUNDS);return await s.Ay.hash(e,t)}catch(e){throw Error("Failed to hash password")}}static async verifyPassword(e,t){try{return await s.Ay.compare(e,t)}catch(e){throw Error("Failed to verify password")}}}class c{static generateToken(e){try{return n().sign(e,i(),{expiresIn:o,algorithm:"HS256"})}catch(e){throw Error("Failed to generate JWT token")}}static verifyToken(e){try{return n().verify(e,i(),{algorithms:["HS256"]})}catch(e){if(e instanceof n().TokenExpiredError)throw Error("Token has expired");if(e instanceof n().JsonWebTokenError)throw Error("Invalid token");throw Error("Token verification failed")}}static extractTokenFromHeader(e){return e&&e.startsWith("Bearer ")?e.substring(7):null}}let l={NAME:"haq_auth_token",OPTIONS:{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:604800,path:"/"}};class d{static isValidEmail(e){return/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)&&e.length<=255}static isValidUsername(e){return/^[a-zA-Z0-9_]{3,50}$/.test(e)}static validatePassword(e){let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),e.length>128&&t.push("Password must be less than 128 characters"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[0-9]/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e)||t.push("Password must contain at least one special character"),{isValid:0===t.length,errors:t}}static sanitizeInput(e){return e.trim().replace(/[<>]/g,"").substring(0,1e3)}}},23633:(e,t,r)=>{"use strict";r.d(t,{ix:()=>i});var s=r(32190),a=r(51641);async function n(e){try{if(!await a.b.isAuthenticated())return{success:!1,error:"User is not authenticated",status:401};if(!await a.b.isAdmin())return{success:!1,error:"Unauthorized access: User does not have admin privileges",status:401};let e=await a.b.getCurrentUser();if(!e)return{success:!1,error:"Failed to retrieve user data",status:401};return{success:!0,user:e}}catch(e){return console.error("Admin auth verification error:",e),{success:!1,error:"Internal authentication error",status:500}}}function i(e){return async(t,r={})=>{let a=await n(t);if(!a.success)return s.NextResponse.json({success:!1,message:a.error||"Authentication failed"},{status:a.status||401});try{return await e(t,r,a.user)}catch(e){return console.error("Admin API handler error:",e),s.NextResponse.json({success:!1,message:"Internal server error"},{status:500})}}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51641:(e,t,r)=>{"use strict";r.d(t,{L:()=>i,b:()=>o});var s=r(44999),a=r(12909),n=r(56621);class i{static async setAuthCookie(e){(await (0,s.UL)()).set(a.Qi.NAME,e,a.Qi.OPTIONS)}static async getAuthToken(){try{let e=(await (0,s.UL)()).get(a.Qi.NAME);return e?.value||null}catch(e){return null}}static async removeAuthCookie(){(await (0,s.UL)()).delete(a.Qi.NAME)}}class o{static async getCurrentUser(){try{let e=await i.getAuthToken();if(!e)return null;let t=a.DU.verifyToken(e),{data:r,error:s}=await n.ND.from("haq_users_db.users").select("user_id, username, email, role, created_at, updated_at").eq("user_id",t.user_id).limit(1);if(s||!r||0===r.length)return null;return r[0]}catch(e){return null}}static async isAuthenticated(){return null!==await this.getCurrentUser()}static async isAdmin(){try{let e=await i.getAuthToken();if(!e)return!1;let t=a.DU.verifyToken(e);return"admin"===t.role}catch(e){return!1}}static async verifyTokenAndGetUser(e){try{let t=a.DU.verifyToken(e),{data:r,error:s}=await n.ND.from("haq_users_db.users").select("user_id, username, email, role, created_at, updated_at").eq("user_id",t.user_id).limit(1);if(s||!r||0===r.length)return null;return r[0]}catch(e){return null}}}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{ND:()=>n});var s=r(39398);r(98766);let a=null,n=a=(0,s.createClient)("https://wqbuilazpyxpwyuwuqpi.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndxYnVpbGF6cHl4cHd5dXd1cXBpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1NTYyNDMsImV4cCI6MjA2NjEzMjI0M30.GeRI54Rskwdbfm9_lRxy1-7YQ8vA74JWblNF2GRpzrI")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},99911:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>q,routeModule:()=>f,serverHooks:()=>A,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{DELETE:()=>_,GET:()=>h,PATCH:()=>w,POST:()=>g,PUT:()=>y});var a=r(96559),n=r(48088),i=r(37719),o=r(32190),u=r(23633),c=r(56621),l=r(45697);let d=l.z.object({name:l.z.string().min(1,"Company name is required").max(255,"Company name too long"),industry:l.z.string().optional(),hq_location:l.z.string().optional(),description:l.z.string().optional(),website_url:l.z.string().url("Invalid website URL").optional().or(l.z.literal("")),logo_url:l.z.string().url("Invalid logo URL").optional().or(l.z.literal("")),employee_count_range:l.z.string().optional(),founded_year:l.z.number().int().min(1800).max(new Date().getFullYear()).optional()});async function p(e,t,r){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),s=parseInt(t.get("limit")||"20"),a=t.get("search")||"",n=t.get("industry")||"",i=(r-1)*s,u=c.ND.from("haq_content_db.companies").select(`
        company_id,
        name,
        slug,
        industry,
        location,
        description,
        website_url,
        logo_url,
        employee_count_range,
        founded_year,
        haq_score,
        total_reviews,
        is_verified,
        created_at,
        updated_at
      `).order("created_at",{ascending:!1});a&&(u=u.ilike("name",`%${a}%`)),n&&(u=u.eq("industry",n)),u=u.range(i,i+s-1);let{data:l,error:d,count:p}=await u;if(d)return console.error("Error fetching companies:",d),o.NextResponse.json({success:!1,message:"Failed to fetch companies"},{status:500});let{count:m}=await c.ND.from("haq_content_db.companies").select("*",{count:"exact",head:!0});return o.NextResponse.json({success:!0,data:{companies:l||[],pagination:{page:r,limit:s,total:m||0,totalPages:Math.ceil((m||0)/s)}}})}catch(e){return console.error("Get companies error:",e),o.NextResponse.json({success:!1,message:"Internal server error"},{status:500})}}async function m(e,t,r){try{let t=await e.json(),r=d.safeParse(t);if(!r.success)return o.NextResponse.json({success:!1,message:"Validation failed",errors:r.error.errors},{status:400});let s=r.data,a=s.name.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/^-+|-+$/g,""),{data:n}=await c.ND.from("haq_content_db.companies").select("company_id, name, slug").or(`name.eq.${s.name},slug.eq.${a}`).limit(1);if(n&&n.length>0)return o.NextResponse.json({success:!1,message:"Company with this name already exists"},{status:409});let{data:i,error:u}=await c.ND.from("haq_content_db.companies").insert({name:s.name,slug:a,industry:s.industry||null,location:s.hq_location||null,description:s.description||null,website_url:s.website_url||null,logo_url:s.logo_url||null,employee_count_range:s.employee_count_range||null,founded_year:s.founded_year||null,haq_score:0,total_reviews:0,is_verified:!1}).select().single();if(u)return console.error("Error creating company:",u),o.NextResponse.json({success:!1,message:"Failed to create company"},{status:500});return o.NextResponse.json({success:!0,message:"Company created successfully",data:i},{status:201})}catch(e){return console.error("Create company error:",e),o.NextResponse.json({success:!1,message:"Internal server error"},{status:500})}}let h=(0,u.ix)(p),g=(0,u.ix)(m);async function y(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405})}async function _(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405})}async function w(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405})}let f=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/companies/route",pathname:"/api/admin/companies",filename:"route",bundlePath:"app/api/admin/companies/route"},resolvedPagePath:"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\admin\\companies\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:v,serverHooks:A}=f;function q(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:v})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,573,358,697],()=>r(99911));module.exports=s})();