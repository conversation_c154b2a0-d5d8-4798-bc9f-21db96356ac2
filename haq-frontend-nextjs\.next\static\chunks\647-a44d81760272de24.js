"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[647],{283:(e,t,a)=>{a.d(t,{As:()=>d,AuthProvider:()=>c});var s=a(5155),r=a(2115);let l={user:null,isLoading:!0,isAuthenticated:!1,error:null};function n(e,t){switch(t.type){case"AUTH_START":return{...e,isLoading:!0,error:null};case"AUTH_SUCCESS":return{...e,user:t.payload,isAuthenticated:!0,isLoading:!1,error:null};case"AUTH_FAILURE":return{...e,user:null,isAuthenticated:!1,isLoading:!1,error:t.payload};case"AUTH_LOGOUT":return{...e,user:null,isAuthenticated:!1,isLoading:!1,error:null};case"CLEAR_ERROR":return{...e,error:null};case"SET_LOADING":return{...e,isLoading:t.payload};default:return e}}let i=(0,r.createContext)(null);function c(e){let{children:t}=e,[a,c]=(0,r.useReducer)(n,l);(0,r.useEffect)(()=>{d()},[]);let d=(0,r.useCallback)(async()=>{try{c({type:"SET_LOADING",payload:!0});let e=await fetch("/api/auth/me",{method:"GET",credentials:"include"});if(e.ok){let t=await e.json();t.success&&t.user?c({type:"AUTH_SUCCESS",payload:t.user}):c({type:"AUTH_LOGOUT"})}else c({type:"AUTH_LOGOUT"})}catch(e){c({type:"AUTH_LOGOUT"})}finally{c({type:"SET_LOADING",payload:!1})}},[]),o=(0,r.useCallback)(async(e,t)=>{try{c({type:"AUTH_START"});let a=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:e,password:t})}),s=await a.json();if(s.success&&s.user)return c({type:"AUTH_SUCCESS",payload:s.user}),{success:!0,message:s.message};return c({type:"AUTH_FAILURE",payload:s.message}),{success:!1,message:s.message}}catch(t){let e="Network error. Please try again.";return c({type:"AUTH_FAILURE",payload:e}),{success:!1,message:e}}},[]),h=(0,r.useCallback)(async(e,t,a)=>{try{c({type:"AUTH_START"});let s=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({username:e,email:t,password:a})}),r=await s.json();if(r.success&&r.user)return c({type:"AUTH_SUCCESS",payload:r.user}),{success:!0,message:r.message};return c({type:"AUTH_FAILURE",payload:r.message}),{success:!1,message:r.message}}catch(t){let e="Network error. Please try again.";return c({type:"AUTH_FAILURE",payload:e}),{success:!1,message:e}}},[]),u=(0,r.useCallback)(async()=>{try{c({type:"SET_LOADING",payload:!0}),await fetch("/api/auth/logout",{method:"POST",credentials:"include"}),c({type:"AUTH_LOGOUT"})}catch(e){c({type:"AUTH_LOGOUT"})}finally{c({type:"SET_LOADING",payload:!1})}},[]),m=(0,r.useCallback)(()=>{c({type:"CLEAR_ERROR"})},[]),x=(0,r.useCallback)(async()=>{await d()},[d]),y=(0,r.useMemo)(()=>({...a,login:o,register:h,logout:u,clearError:m,refreshUser:x}),[a,o,h,u,m,x]);return(0,s.jsx)(i.Provider,{value:y,children:t})}function d(){let e=(0,r.useContext)(i);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},381:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3227:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(9946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},4416:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},4835:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5525:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},6647:(e,t,a)=>{a.d(t,{A:()=>g});var s=a(5155),r=a(2115),l=a(6874),n=a.n(l),i=a(5695),c=a(283);let d=(0,a(9946).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]]);var o=a(7434),h=a(3227),u=a(7580),m=a(381),x=a(5525),y=a(4416),p=a(4835),f=a(4783);let g=e=>{let{children:t,title:a="Admin Dashboard"}=e,[l,g]=(0,r.useState)(!1),{user:A,logout:v}=(0,c.As)(),j=(0,i.useRouter)(),b=(null==A?void 0:A.role)==="admin";r.useEffect(()=>{A&&!b&&j.push("/")},[A,b,j]);let k=async()=>{await v(),j.push("/")},N=[{name:"Dashboard",href:"/admin",icon:d,current:!1},{name:"Moderation Queue",href:"/admin/moderation",icon:o.A,current:!1},{name:"Companies",href:"/admin/companies",icon:h.A,current:!1},{name:"Users",href:"/admin/users",icon:u.A,current:!1},{name:"Settings",href:"/admin/settings",icon:m.A,current:!1}];return b?(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsxs)("div",{className:"fixed inset-0 z-40 lg:hidden ".concat(l?"block":"hidden"),children:[(0,s.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>g(!1)}),(0,s.jsxs)("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-white",children:[(0,s.jsx)("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:(0,s.jsx)("button",{type:"button",className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white",onClick:()=>g(!1),children:(0,s.jsx)(y.A,{className:"h-6 w-6 text-white"})})}),(0,s.jsxs)("div",{className:"flex-1 h-0 pt-5 pb-4 overflow-y-auto",children:[(0,s.jsx)("div",{className:"flex-shrink-0 flex items-center px-4",children:(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"HAQ Admin"})}),(0,s.jsx)("nav",{className:"mt-5 px-2 space-y-1",children:N.map(e=>(0,s.jsxs)(n(),{href:e.href,className:"group flex items-center px-2 py-2 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900",children:[(0,s.jsx)(e.icon,{className:"mr-4 h-6 w-6"}),e.name]},e.name))})]}),(0,s.jsx)("div",{className:"flex-shrink-0 flex border-t border-gray-200 p-4",children:(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("p",{className:"text-base font-medium text-gray-700",children:null==A?void 0:A.username}),(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Admin"})]})})})]})]}),(0,s.jsx)("div",{className:"hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0",children:(0,s.jsxs)("div",{className:"flex-1 flex flex-col min-h-0 border-r border-gray-200 bg-white",children:[(0,s.jsxs)("div",{className:"flex-1 flex flex-col pt-5 pb-4 overflow-y-auto",children:[(0,s.jsx)("div",{className:"flex items-center flex-shrink-0 px-4",children:(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"HAQ Admin"})}),(0,s.jsx)("nav",{className:"mt-5 flex-1 px-2 bg-white space-y-1",children:N.map(e=>(0,s.jsxs)(n(),{href:e.href,className:"group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900",children:[(0,s.jsx)(e.icon,{className:"mr-3 h-5 w-5"}),e.name]},e.name))})]}),(0,s.jsx)("div",{className:"flex-shrink-0 flex border-t border-gray-200 p-4",children:(0,s.jsxs)("div",{className:"flex items-center w-full",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-700",children:null==A?void 0:A.username}),(0,s.jsx)("p",{className:"text-xs font-medium text-gray-500",children:"Admin"})]}),(0,s.jsx)("button",{onClick:k,className:"ml-3 flex items-center justify-center h-8 w-8 rounded-full text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500",title:"Logout",children:(0,s.jsx)(p.A,{className:"h-4 w-4"})})]})})]})}),(0,s.jsxs)("div",{className:"lg:pl-64 flex flex-col flex-1",children:[(0,s.jsx)("div",{className:"sticky top-0 z-10 lg:hidden pl-1 pt-1 sm:pl-3 sm:pt-3 bg-gray-50",children:(0,s.jsx)("button",{type:"button",className:"-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",onClick:()=>g(!0),children:(0,s.jsx)(f.A,{className:"h-6 w-6"})})}),(0,s.jsx)("div",{className:"bg-white shadow",children:(0,s.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"py-6",children:(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:a})})})}),(0,s.jsx)("main",{className:"flex-1",children:(0,s.jsx)("div",{className:"py-6",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:t})})})]})]}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(x.A,{className:"mx-auto h-12 w-12 text-red-500 mb-4"}),(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have permission to access this area."}),(0,s.jsx)(n(),{href:"/",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"Return to Home"})]})})}},7434:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7580:(e,t,a)=>{a.d(t,{A:()=>s});let s=(0,a(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9946:(e,t,a)=>{a.d(t,{A:()=>h});var s=a(2115);let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)((e,t)=>{let{color:a="currentColor",size:r=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:o="",children:h,iconNode:u,...m}=e;return(0,s.createElement)("svg",{ref:t,...d,width:r,height:r,stroke:a,strokeWidth:n?24*Number(l)/Number(r):l,className:i("lucide",o),...!h&&!c(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[t,a]=e;return(0,s.createElement)(t,a)}),...Array.isArray(h)?h:[h]])}),h=(e,t)=>{let a=(0,s.forwardRef)((a,l)=>{let{className:c,...d}=a;return(0,s.createElement)(o,{ref:l,iconNode:t,className:i("lucide-".concat(r(n(e))),"lucide-".concat(e),c),...d})});return a.displayName=n(e),a}}}]);