(()=>{var e={};e.id=29,e.ids=[29],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12909:(e,t,r)=>{"use strict";r.d(t,{DU:()=>u,Qi:()=>d,h8:()=>c,ne:()=>l});var s=r(85663),a=r(43205),n=r.n(a);let i=()=>{let e=process.env.JWT_SECRET;if(!e)throw Error("JWT_SECRET environment variable is required");return e},o=process.env.JWT_EXPIRES_IN||"7d";class c{static{this.SALT_ROUNDS=12}static async hashPassword(e){try{let t=await s.Ay.genSalt(this.SALT_ROUNDS);return await s.Ay.hash(e,t)}catch(e){throw Error("Failed to hash password")}}static async verifyPassword(e,t){try{return await s.Ay.compare(e,t)}catch(e){throw Error("Failed to verify password")}}}class u{static generateToken(e){try{return n().sign(e,i(),{expiresIn:o,algorithm:"HS256"})}catch(e){throw Error("Failed to generate JWT token")}}static verifyToken(e){try{return n().verify(e,i(),{algorithms:["HS256"]})}catch(e){if(e instanceof n().TokenExpiredError)throw Error("Token has expired");if(e instanceof n().JsonWebTokenError)throw Error("Invalid token");throw Error("Token verification failed")}}static extractTokenFromHeader(e){return e&&e.startsWith("Bearer ")?e.substring(7):null}}let d={NAME:"haq_auth_token",OPTIONS:{httpOnly:!0,secure:!0,sameSite:"strict",maxAge:604800,path:"/"}};class l{static isValidEmail(e){return/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e)&&e.length<=255}static isValidUsername(e){return/^[a-zA-Z0-9_]{3,50}$/.test(e)}static validatePassword(e){let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),e.length>128&&t.push("Password must be less than 128 characters"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[0-9]/.test(e)||t.push("Password must contain at least one number"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(e)||t.push("Password must contain at least one special character"),{isValid:0===t.length,errors:t}}static sanitizeInput(e){return e.trim().replace(/[<>]/g,"").substring(0,1e3)}}},19537:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>g,serverHooks:()=>_,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{DELETE:()=>h,GET:()=>l,PATCH:()=>w,POST:()=>p,PUT:()=>m});var a=r(96559),n=r(48088),i=r(37719),o=r(32190),c=r(23633),u=r(56621);async function d(e,t,r){try{let t,{searchParams:r}=new URL(e.url),s=Math.max(1,parseInt(r.get("page")||"1",10)),a=Math.min(100,Math.max(1,parseInt(r.get("limit")||"20",10))),n=r.get("sort")||"newest",i=(s-1)*a;if(!["newest","oldest","company_name"].includes(n))return o.NextResponse.json({success:!1,message:"Invalid sort parameter. Must be one of: newest, oldest, company_name"},{status:400});switch(n){case"oldest":t={column:"created_at",ascending:!0};break;case"company_name":t={column:"companies.name",ascending:!0};break;default:t={column:"created_at",ascending:!1}}let{data:c,error:d,count:l}=await u.ND.from("haq_content_db.reviews").select(`
        review_id,
        company_id,
        overall_rating,
        title,
        pros,
        cons,
        advice_to_management,
        is_approved,
        created_at,
        updated_at
      `,{count:"exact"}).eq("is_approved",!1).order(t.column,{ascending:t.ascending}).range(i,i+a-1);if(d)return console.error("Error fetching pending reviews:",d),o.NextResponse.json({success:!1,message:"Failed to fetch pending reviews"},{status:500});let p=[...new Set(c?.map(e=>e.company_id)||[])],{data:m,error:h}=await u.ND.from("haq_content_db.companies").select("company_id, name, industry, location").in("company_id",p);if(h)return console.error("Error fetching companies:",h),o.NextResponse.json({success:!1,message:"Failed to fetch company information"},{status:500});let w=new Map(m?.map(e=>[e.company_id,e])||[]),g=c?.map(e=>{let t=w.get(e.company_id);return{review_id:e.review_id,company:t?{company_id:t.company_id,name:t.name,industry:t.industry,location:t.location}:null,overall_rating:e.overall_rating,title:e.title,pros:e.pros,cons:e.cons,advice_to_management:e.advice_to_management,is_approved:e.is_approved,created_at:e.created_at,updated_at:e.updated_at}})||[],y=l||0,v=Math.ceil(y/a);return o.NextResponse.json({success:!0,data:{reviews:g,pagination:{page:s,limit:a,total:y,totalPages:v,hasNext:s<v,hasPrev:s>1},sort:n,filters:{status:"pending"}}},{headers:{"Cache-Control":"private, max-age=60, s-maxage=60",Vary:"Accept-Encoding","X-Content-Type-Options":"nosniff","X-Frame-Options":"DENY"}})}catch(e){return console.error("Admin pending reviews error:",e),o.NextResponse.json({success:!1,message:"Internal server error"},{status:500})}}let l=(0,c.ix)(d);async function p(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function m(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function h(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}async function w(){return o.NextResponse.json({success:!1,message:"Method not allowed"},{status:405,headers:{Allow:"GET"}})}let g=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/reviews/pending/route",pathname:"/api/admin/reviews/pending",filename:"route",bundlePath:"app/api/admin/reviews/pending/route"},resolvedPagePath:"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\api\\admin\\reviews\\pending\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:y,workUnitAsyncStorage:v,serverHooks:_}=g;function f(){return(0,i.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:v})}},23633:(e,t,r)=>{"use strict";r.d(t,{ix:()=>i});var s=r(32190),a=r(51641);async function n(e){try{if(!await a.b.isAuthenticated())return{success:!1,error:"User is not authenticated",status:401};if(!await a.b.isAdmin())return{success:!1,error:"Unauthorized access: User does not have admin privileges",status:401};let e=await a.b.getCurrentUser();if(!e)return{success:!1,error:"Failed to retrieve user data",status:401};return{success:!0,user:e}}catch(e){return console.error("Admin auth verification error:",e),{success:!1,error:"Internal authentication error",status:500}}}function i(e){return async(t,r={})=>{let a=await n(t);if(!a.success)return s.NextResponse.json({success:!1,message:a.error||"Authentication failed"},{status:a.status||401});try{return await e(t,r,a.user)}catch(e){return console.error("Admin API handler error:",e),s.NextResponse.json({success:!1,message:"Internal server error"},{status:500})}}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51641:(e,t,r)=>{"use strict";r.d(t,{L:()=>i,b:()=>o});var s=r(44999),a=r(12909),n=r(56621);class i{static async setAuthCookie(e){(await (0,s.UL)()).set(a.Qi.NAME,e,a.Qi.OPTIONS)}static async getAuthToken(){try{let e=(await (0,s.UL)()).get(a.Qi.NAME);return e?.value||null}catch(e){return null}}static async removeAuthCookie(){(await (0,s.UL)()).delete(a.Qi.NAME)}}class o{static async getCurrentUser(){try{let e=await i.getAuthToken();if(!e)return null;let t=a.DU.verifyToken(e),{data:r,error:s}=await n.ND.from("haq_users_db.users").select("user_id, username, email, role, created_at, updated_at").eq("user_id",t.user_id).limit(1);if(s||!r||0===r.length)return null;return r[0]}catch(e){return null}}static async isAuthenticated(){return null!==await this.getCurrentUser()}static async isAdmin(){try{let e=await i.getAuthToken();if(!e)return!1;let t=a.DU.verifyToken(e);return"admin"===t.role}catch(e){return!1}}static async verifyTokenAndGetUser(e){try{let t=a.DU.verifyToken(e),{data:r,error:s}=await n.ND.from("haq_users_db.users").select("user_id, username, email, role, created_at, updated_at").eq("user_id",t.user_id).limit(1);if(s||!r||0===r.length)return null;return r[0]}catch(e){return null}}}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{ND:()=>n});var s=r(39398);r(98766);let a=null,n=a=(0,s.createClient)("https://wqbuilazpyxpwyuwuqpi.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndxYnVpbGF6cHl4cHd5dXd1cXBpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1NTYyNDMsImV4cCI6MjA2NjEzMjI0M30.GeRI54Rskwdbfm9_lRxy1-7YQ8vA74JWblNF2GRpzrI")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,573,358],()=>r(19537));module.exports=s})();