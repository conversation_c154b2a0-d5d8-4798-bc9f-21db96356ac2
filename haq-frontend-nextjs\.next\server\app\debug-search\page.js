(()=>{var e={};e.id=93,e.ids=[93],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22884:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=t(65239),a=t(48088),n=t(88170),i=t.n(n),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["debug-search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,84125)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\debug-search\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,82893)),"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\debug-search\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/debug-search/page",pathname:"/debug-search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31011:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(60687),a=t(43210);function n(){let[e,r]=(0,a.useState)("tech"),[t,n]=(0,a.useState)(null),[i,o]=(0,a.useState)(!1),[d,l]=(0,a.useState)(null),c=async()=>{o(!0),l(null),n(null);try{let r=await fetch(`/api/search/companies?q=${encodeURIComponent(e)}`),t=await r.json();if(!r.ok)throw Error(`HTTP ${r.status}: ${t.error||"Unknown error"}`);n(t)}catch(e){l(e instanceof Error?e.message:"Unknown error")}finally{o(!1)}},p=async()=>{o(!0),l(null),n(null);try{let r=await fetch(`/api/companies?q=${encodeURIComponent(e)}&limit=5`),t=await r.json();if(!r.ok)throw Error(`HTTP ${r.status}: ${t.error||"Unknown error"}`);n(t)}catch(e){l(e instanceof Error?e.message:"Unknown error")}finally{o(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-background-primary p-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-text-primary mb-8",children:"Search API Debug"}),(0,s.jsxs)("div",{className:"bg-surface-primary border border-border-primary rounded-lg p-6 mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"Test Search APIs"}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-text-primary mb-2",children:"Search Query:"}),(0,s.jsx)("input",{type:"text",value:e,onChange:e=>r(e.target.value),className:"w-full px-3 py-2 border border-border-primary rounded-lg bg-surface-secondary text-text-primary",placeholder:"Enter search query..."})]}),(0,s.jsxs)("div",{className:"flex space-x-4 mb-6",children:[(0,s.jsx)("button",{onClick:c,disabled:i,className:"px-4 py-2 bg-accent-primary text-text-on-accent rounded-lg hover:bg-accent-secondary disabled:opacity-50",children:"Test Search API"}),(0,s.jsx)("button",{onClick:p,disabled:i,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50",children:"Test Companies API"})]}),i&&(0,s.jsx)("div",{className:"text-text-secondary",children:"Loading..."}),d&&(0,s.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:[(0,s.jsx)("strong",{children:"Error:"})," ",d]}),t&&(0,s.jsxs)("div",{className:"bg-surface-secondary border border-border-primary rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-text-primary mb-2",children:"Results:"}),(0,s.jsx)("pre",{className:"text-sm text-text-secondary overflow-auto max-h-96",children:JSON.stringify(t,null,2)})]})]}),(0,s.jsxs)("div",{className:"bg-surface-primary border border-border-primary rounded-lg p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-text-primary mb-4",children:"API Endpoints"}),(0,s.jsxs)("ul",{className:"space-y-2 text-text-secondary",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Search API:"})," GET /api/search/companies?q=tech"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Companies API:"})," GET /api/companies?q=tech&limit=5"]})]})]})]})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},47430:(e,r,t)=>{Promise.resolve().then(t.bind(t,31011))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78974:(e,r,t)=>{Promise.resolve().then(t.bind(t,84125))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84125:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\Haq website v1\\\\haq-frontend-nextjs\\\\src\\\\app\\\\debug-search\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\Haq website v1\\haq-frontend-nextjs\\src\\app\\debug-search\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,942,658,647],()=>t(22884));module.exports=s})();